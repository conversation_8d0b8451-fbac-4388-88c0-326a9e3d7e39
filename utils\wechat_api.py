#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_api.py
<AUTHOR> AI Assistant
@Date     : 2025/07/23
@File_Desc: 微信小程序API工具类 - 核心功能实现
"""

import hashlib
import hmac
import json
import logging
import time
from typing import Dict, Any, Optional, Tuple
from urllib.parse import urlencode

import requests
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

logger = logging.getLogger(__name__)


class WechatAPIError(Exception):
    """微信API异常类"""
    
    def __init__(self, errcode: int, errmsg: str, api_name: str = ""):
        self.errcode = errcode
        self.errmsg = errmsg
        self.api_name = api_name
        super().__init__(f"微信API错误 [{api_name}]: {errcode} - {errmsg}")


class WechatAPIRateLimiter:
    """微信API频率限制器"""
    
    @staticmethod
    def check_rate_limit(api_name: str, limit_per_minute: int = 60) -> bool:
        """
        检查API调用频率限制
        
        :param api_name: API名称
        :param limit_per_minute: 每分钟限制次数
        :return: 是否允许调用
        """
        cache_key = f"wechat_api_rate_limit:{api_name}"
        current_count = cache.get(cache_key, 0)
        
        if current_count >= limit_per_minute:
            logger.warning(f"微信API {api_name} 调用频率超限: {current_count}/{limit_per_minute}")
            return False
        
        # 增加计数，设置60秒过期
        cache.set(cache_key, current_count + 1, 60)
        return True
    
    @staticmethod
    def get_remaining_calls(api_name: str, limit_per_minute: int = 60) -> int:
        """获取剩余调用次数"""
        cache_key = f"wechat_api_rate_limit:{api_name}"
        current_count = cache.get(cache_key, 0)
        return max(0, limit_per_minute - current_count)


class WechatMiniProgramAPI:
    """微信小程序API工具类"""
    
    # API端点配置
    CODE2SESSION_URL = "https://api.weixin.qq.com/sns/jscode2session"
    CHECK_SESSION_URL = "https://api.weixin.qq.com/wxa/checksession"
    RESET_SESSION_URL = "https://api.weixin.qq.com/wxa/resetusersessionkey"
    ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token"
    
    # 频率限制配置
    RATE_LIMITS = {
        'code2session': 60,      # 每分钟60次
        'checksession': 100,     # 每分钟100次
        'resetsession': 10,      # 每分钟10次（较严格）
        'access_token': 200,     # 每分钟200次
    }
    
    def __init__(self, app_id: str = None, app_secret: str = None):
        """
        初始化微信小程序API客户端
        
        :param app_id: 微信小程序AppID
        :param app_secret: 微信小程序AppSecret
        """
        self.app_id = app_id or getattr(settings, 'WECHAT_MINIPROGRAM_APP_ID', '')
        self.app_secret = app_secret or getattr(settings, 'WECHAT_MINIPROGRAM_APP_SECRET', '')
        
        if not self.app_id or not self.app_secret:
            raise ValueError("微信小程序AppID和AppSecret不能为空")
        
        self.session = requests.Session()
        self.session.timeout = 30  # 30秒超时
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'WechatMiniProgram-Django/1.0',
            'Content-Type': 'application/json',
        })
    
    def _check_rate_limit(self, api_name: str) -> None:
        """检查API调用频率限制"""
        limit = self.RATE_LIMITS.get(api_name, 60)
        if not WechatAPIRateLimiter.check_rate_limit(api_name, limit):
            raise WechatAPIError(
                errcode=45011,
                errmsg="API调用频率超限，请稍后重试",
                api_name=api_name
            )
    
    def _make_request(self, url: str, params: Dict[str, Any], 
                     api_name: str, max_retries: int = 3) -> Dict[str, Any]:
        """
        发起HTTP请求并处理响应
        
        :param url: 请求URL
        :param params: 请求参数
        :param api_name: API名称（用于日志和缓存）
        :param max_retries: 最大重试次数
        :return: 响应数据
        """
        self._check_rate_limit(api_name)
        
        for attempt in range(max_retries):
            try:
                logger.info(f"调用微信API {api_name}, 尝试 {attempt + 1}/{max_retries}")
                
                response = self.session.get(url, params=params)
                response.raise_for_status()
                
                data = response.json()
                
                # 检查微信API错误码
                if 'errcode' in data and data['errcode'] != 0:
                    raise WechatAPIError(
                        errcode=data['errcode'],
                        errmsg=data.get('errmsg', '未知错误'),
                        api_name=api_name
                    )
                
                logger.info(f"微信API {api_name} 调用成功")
                return data
                
            except requests.exceptions.RequestException as e:
                logger.warning(f"微信API {api_name} 网络请求失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == max_retries - 1:
                    raise WechatAPIError(
                        errcode=-1,
                        errmsg=f"网络请求失败: {str(e)}",
                        api_name=api_name
                    )
                time.sleep(2 ** attempt)  # 指数退避
                
            except (ValueError, json.JSONDecodeError) as e:
                logger.error(f"微信API {api_name} 响应解析失败: {str(e)}")
                raise WechatAPIError(
                    errcode=-2,
                    errmsg=f"响应解析失败: {str(e)}",
                    api_name=api_name
                )
    
    def code2session(self, js_code: str) -> Dict[str, Any]:
        """
        登录凭证校验 - 将小程序code换取session_key和openid

        :param js_code: 小程序登录时获取的code
        :return: 包含session_key、openid等信息的字典，其中openid为微信小程序用户唯一标识（只针对当前的小程序有效）
        """
        params = {
            'appid': self.app_id,
            'secret': self.app_secret,
            'js_code': js_code,
            'grant_type': 'authorization_code'
        }
        
        result = self._make_request(
            url=self.CODE2SESSION_URL,
            params=params,
            api_name='code2session'
        )
        
        # 缓存session_key（如果存在）
        if 'session_key' in result and 'openid' in result:  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
            cache_key = f"wechat_session:{result['openid']}"  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
            cache.set(cache_key, result['session_key'], 7200)  # 缓存2小时
        
        return result
    
    def _generate_signature(self, session_key: str, data: str = "") -> str:
        """
        生成HMAC-SHA256签名
        
        :param session_key: 微信会话密钥
        :param data: 要签名的数据（默认为空字符串）
        :return: 签名字符串
        """
        return hmac.new(
            session_key.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def check_session_key(self, openid: str, session_key: str) -> bool:
        """
        检验登录态session_key是否有效
        
        :param openid: 用户唯一标识
        :param session_key: 会话密钥
        :return: session_key是否有效
        """
        # 生成签名
        signature = self._generate_signature(session_key)
        
        # 获取access_token
        access_token = self._get_access_token()
        
        params = {
            'access_token': access_token,
            'openid': openid,
            'signature': signature,
            'sig_method': 'hmac_sha256'
        }
        
        try:
            self._make_request(
                url=self.CHECK_SESSION_URL,
                params=params,
                api_name='checksession'
            )
            return True
        except WechatAPIError as e:
            if e.errcode == 87009:  # invalid signature
                logger.info(f"用户 {openid} 的session_key已失效")
                return False
            raise
    
    def reset_user_session_key(self, openid: str, session_key: str) -> str:
        """
        重置用户登录态session_key
        
        :param openid: 用户唯一标识
        :param session_key: 当前会话密钥
        :return: 新的session_key
        """
        # 生成签名
        signature = self._generate_signature(session_key)
        
        # 获取access_token
        access_token = self._get_access_token()
        
        params = {
            'access_token': access_token,
            'openid': openid,
            'signature': signature,
            'sig_method': 'hmac_sha256'
        }
        
        result = self._make_request(
            url=self.RESET_SESSION_URL,
            params=params,
            api_name='resetsession'
        )
        
        new_session_key = result.get('session_key')
        if new_session_key:
            # 更新缓存
            cache_key = f"wechat_session:{openid}"
            cache.set(cache_key, new_session_key, 7200)
        
        return new_session_key
    
    def _get_access_token(self) -> str:
        """
        获取access_token（带缓存）
        
        :return: access_token
        """
        cache_key = f"wechat_access_token:{self.app_id}"
        access_token = cache.get(cache_key)
        
        if access_token:
            return access_token
        
        params = {
            'grant_type': 'client_credential',
            'appid': self.app_id,
            'secret': self.app_secret
        }
        
        result = self._make_request(
            url=self.ACCESS_TOKEN_URL,
            params=params,
            api_name='access_token'
        )
        
        access_token = result.get('access_token')
        expires_in = result.get('expires_in', 7200)
        
        if access_token:
            # 缓存access_token，提前5分钟过期以避免边界问题
            cache.set(cache_key, access_token, expires_in - 300)
        
        return access_token


# 全局实例（单例模式）
_wechat_api_instance = None


def get_wechat_api() -> WechatMiniProgramAPI:
    """
    获取微信API实例（单例模式）
    
    :return: WechatMiniProgramAPI实例
    """
    global _wechat_api_instance
    if _wechat_api_instance is None:
        _wechat_api_instance = WechatMiniProgramAPI()
    return _wechat_api_instance
