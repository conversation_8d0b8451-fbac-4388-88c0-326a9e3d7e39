#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : user_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""
# 标准库导入

# 第三方库导入
import django_filters
from rest_framework import filters, mixins, viewsets
from rest_framework.permissions import IsAuthenticated

# 本地应用导入
from apps.user.filters import UserFilter
from apps.user.models import User
from apps.user.serializers.user_serializer import (
    UserCreateSerializer,
    UserOutputSerializer,
    UserUpdateSerializer,
)
from utils.ajax_result import AjaxResult
from utils.decorator import user_check
from utils.ip_whitelist_permission import IPWhitelistPermission
from utils.pagination import MyPageNumberPagination
from utils.user_check import is_admin


class UserViewSet(
    viewsets.GenericViewSet,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    mixins.CreateModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
):
    """
    用户管理视图集

    提供系统用户的完整CRUD操作，包括用户的创建、查询、更新和删除功能。
    支持用户基本信息管理、角色分配、权限配置、人脸核身信息管理、微信小程序
    用户绑定等功能，用于系统用户账户的全生命周期管理。

    新增功能：
    - 人脸核身信息管理：支持用户真实姓名验证、核身状态跟踪
    - 微信小程序集成：支持微信用户绑定、会话管理、用户信息同步
    """

    queryset = (
        User.objects.prefetch_related("groups")
        .prefetch_related("user_permissions")
        .prefetch_related("groups__permissions")
        .all()
    )
    filterset_class = UserFilter

    permission_classes = [IPWhitelistPermission | IsAuthenticated]

    pagination_class = MyPageNumberPagination
    filter_backends = [
        filters.OrderingFilter,
        django_filters.rest_framework.DjangoFilterBackend,
        filters.SearchFilter,
    ]

    ordering_fields = ["id", "username"]
    ordering = ["id"]
    search_fields = ["username", "phone_number", "groups__name"]

    def get_serializer_class(self):
        if self.action in ["create"]:
            return UserCreateSerializer
        elif self.action in ["list", "retrieve"]:
            return UserOutputSerializer
        elif self.action in ["update", "partial_update"]:
            return UserUpdateSerializer
        return UserOutputSerializer

    def get_queryset(self):
        # 过滤掉超级管理员和未激活的用户
        query_set = super().get_queryset()
        return query_set.filter(is_active=True).exclude(is_superuser=True)

    def list(self, request, *args, **kwargs):
        """
        获取用户列表

        查询系统中所有激活用户信息，支持分页查询、排序、搜索和过滤功能。
        返回用户的基本信息、角色和部门归属，用于用户管理和权限分配。

        请求参数:
            查询参数:
                - page (int, 可选): 页码，默认为1
                - page_size (int, 可选): 每页数量，默认为10
                - ordering (str, 可选): 排序字段，可选值: "id", "username", "-id", "-username"
                - search (str, 可选): 搜索关键词，支持用户名、手机号、角色名搜索
                - groups (int, 可选): 按角色ID过滤
                - is_active (bool, 可选): 按激活状态过滤

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "count": 总记录数,
                    "next": "下一页URL",
                    "previous": "上一页URL",
                    "results": [
                        {
                            "id": 用户ID,
                            "username": "用户名",
                            "email": "邮箱地址",
                            "phone_number": "手机号码",
                            "id_card_number": "身份证号",
                            "is_active": 是否激活,
                            "is_staff": 是否员工,
                            "is_superuser": 是否超级管理员,
                            "real_name": "真实姓名（人脸核身获得）",
                            "biz_token": "人脸核身业务标识",
                            "detect_auth_time": "人脸核身时间",
                            "detect_auth_result": 人脸核身结果,
                            "wechat_openid": "微信OpenID",
                            "wechat_unionid": "微信UnionID",
                            "wechat_session_key": "微信SessionKey",
                            "wechat_nickname": "微信昵称",
                            "wechat_avatar_url": "微信头像URL",
                            "wechat_bind_time": "微信绑定时间",
                            "groups": [用户组信息],
                            "user_permissions": [用户权限],
                            "role": [角色信息],
                            "department": [部门信息],
                            "user_all_app": ["应用名称列表"]
                        }
                    ]
                }
            }
        """
        return super().list(request, *args, **kwargs)

    @user_check([is_admin])
    def create(self, request, *args, **kwargs):
        """
        创建新用户

        创建一个新的系统用户账户，可为用户分配角色和权限。
        仅限管理员用户操作，创建成功后用户可使用账户登录系统。

        请求参数:
            请求体参数:
                - username (str, 必需): 用户名，必须唯一
                - phone_number (str, 可选): 手机号码，格式为11位数字
                - id_card_number (str, 可选): 身份证号码
                - password (str, 必需): 用户密码
                - groups (list, 可选): 用户角色ID列表
                - user_permissions (list, 可选): 用户权限ID列表

        请求数据示例:
            {
                "username": "newuser",
                "phone_number": "13800138000",
                "id_card_number": "110101199001011234",
                "password": "password123",
                "groups": [1, 2],
                "user_permissions": [1, 3, 5]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": null
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        self.perform_create(serializer)

        return AjaxResult.success()

    @user_check([is_admin])
    def retrieve(self, request, *args, **kwargs):
        """
        获取用户详细信息

        根据用户ID获取指定用户的详细信息，包括基本资料、角色和权限配置。
        仅限管理员用户操作，用于用户信息查看和编辑前的数据获取。

        请求参数:
            路径参数:
                - id (int, 必需): 用户ID

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "id": 用户ID,
                    "username": "用户名",
                    "email": "邮箱地址",
                    "phone_number": "手机号码",
                    "id_card_number": "身份证号",
                    "is_active": 是否激活,
                    "is_staff": 是否员工,
                    "is_superuser": 是否超级管理员,
                    "real_name": "真实姓名（人脸核身获得）",
                    "biz_token": "人脸核身业务标识",
                    "detect_auth_time": "人脸核身时间",
                    "detect_auth_result": 人脸核身结果,
                    "wechat_openid": "微信OpenID",
                    "wechat_unionid": "微信UnionID",
                    "wechat_session_key": "微信SessionKey",
                    "wechat_nickname": "微信昵称",
                    "wechat_avatar_url": "微信头像URL",
                    "wechat_bind_time": "微信绑定时间",
                    "groups": [用户组信息],
                    "user_permissions": [用户权限],
                    "role": [角色信息],
                    "department": [部门信息],
                    "user_all_app": ["应用名称列表"]
                }
            }
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return AjaxResult.success(data=serializer.data)

    @user_check([is_admin])
    def update(self, request, *args, **kwargs):
        """
        更新用户信息

        更新指定用户的完整信息，包括基本资料、角色和权限配置。
        仅限管理员用户操作，支持完整更新和部分更新。

        请求参数:
            路径参数:
                - id (int, 必需): 用户ID
            请求体参数:
                - username (str, 必需): 用户名
                - phone_number (str, 可选): 手机号码
                - id_card_number (str, 可选): 身份证号码
                - groups (list, 可选): 用户角色ID列表
                - user_permissions (list, 可选): 用户权限ID列表

        请求数据示例:
            {
                "username": "updateduser",
                "phone_number": "13900139000",
                "groups": [2, 3],
                "user_permissions": [2, 4, 6]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return AjaxResult.success(msg="更新成功")

    @user_check([is_admin])
    def partial_update(self, request, *args, **kwargs):
        """
        部分更新用户信息

        部分更新指定用户的信息，只需提供需要修改的字段。
        仅限管理员用户操作，适用于单个或少数字段的修改场景。

        请求参数:
            路径参数:
                - id (int, 必需): 用户ID
            请求体参数:
                - username (str, 可选): 用户名
                - phone_number (str, 可选): 手机号码
                - id_card_number (str, 可选): 身份证号码
                - groups (list, 可选): 用户角色ID列表
                - user_permissions (list, 可选): 用户权限ID列表

        请求数据示例:
            {
                "phone_number": "13800138001",
                "groups": [3]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        kwargs["partial"] = True
        return self.update(request, *args, **kwargs)

    @user_check([is_admin])
    def destroy(self, request, *args, **kwargs):
        """
        删除用户

        删除指定的用户账户，删除后用户将无法登录系统。
        仅限管理员用户操作，删除操作不可逆，请谨慎使用。

        请求参数:
            路径参数:
                - id (int, 必需): 要删除的用户ID

        响应数据结构:
            {
                "code": 200,
                "msg": "删除成功",
                "data": null
            }
        """
        instance = self.get_object()
        self.perform_destroy(instance)

        return AjaxResult.success(msg="删除成功")
