#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : ajax_result.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc:
"""


from django.http import JsonResponse
from authsage.settings import AUTH_SERVER_FRONTEND_URL


class AjaxResult:
    def __init__(self, code=200, msg="操作成功", data=None, state=None):
        self.code = code
        self.msg = msg
        self.data = data
        self.state = state if state else "success" if code == 200 else "fail"

    def to_json_response(self):
        response_data = {"code": self.code, "msg": self.msg, "state": self.state}
        if self.data is not None:
            response_data["data"] = self.data
        return JsonResponse(response_data, status=self.code)

    @staticmethod
    def success(msg="操作成功", data=None):
        return AjaxResult(code=200, msg=msg, data=data, state="success").to_json_response()

    @staticmethod
    def fail(msg="请求错误", data=None):
        return AjaxResult(code=400, msg=msg, data=data, state="fail").to_json_response()

    @staticmethod
    def unauthorized(msg="未授权", data=None):
        redirect_url = AUTH_SERVER_FRONTEND_URL
        data = data if data else redirect_url
        return AjaxResult(code=401, msg=msg, data=data, state="fail").to_json_response()

    @staticmethod
    def forbidden(msg="请求被拒绝", data=None):
        return AjaxResult(code=403, msg=msg, data=data, state="fail").to_json_response()

    @staticmethod
    def not_found(msg="资源不存在", data=None):
        return AjaxResult(code=404, msg=msg, data=data, state="fail").to_json_response()

    @staticmethod
    def server_error(msg="服务器错误", data=None):
        return AjaxResult(code=500, msg=msg, data=data, state="fail").to_json_response()
