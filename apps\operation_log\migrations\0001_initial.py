# Generated by Django 4.1.13 on 2025-07-02 14:58

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="LoginLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("username", models.Char<PERSON>ield(max_length=255, verbose_name="操作人")),
                ("department", models.Char<PERSON><PERSON>(max_length=255, verbose_name="部门")),
                ("account", models.Char<PERSON>ield(max_length=255, verbose_name="账号")),
                ("ip", models.CharField(max_length=50, verbose_name="登录IP")),
                ("login_time", models.DateTimeField(verbose_name="登录时间")),
                (
                    "logout_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="退出登录时间"
                    ),
                ),
            ],
            options={
                "verbose_name": "登录日志表",
                "verbose_name_plural": "登录日志表",
                "db_table": "t_login_log",
            },
        ),
        migrations.CreateModel(
            name="OperationLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("revision", models.IntegerField(default=1, verbose_name="版本号")),
                ("created_by", models.IntegerField(null=True, verbose_name="创建人")),
                (
                    "created_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                ("updated_by", models.IntegerField(null=True, verbose_name="更新人")),
                (
                    "updated_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("username", models.CharField(max_length=255, verbose_name="操作人")),
                ("account", models.CharField(max_length=255, verbose_name="账号")),
                (
                    "button",
                    models.CharField(max_length=255, verbose_name="功能按钮名称"),
                ),
                ("kind", models.CharField(max_length=255, verbose_name="功能按钮类型")),
                ("url", models.TextField(verbose_name="操作URL")),
                (
                    "belong",
                    models.CharField(max_length=255, verbose_name="业务所属模块"),
                ),
                ("ip", models.CharField(max_length=50, verbose_name="所在IP")),
            ],
            options={
                "verbose_name": "操作日志表",
                "verbose_name_plural": "操作日志表",
                "db_table": "t_operation_log",
            },
        ),
    ]
