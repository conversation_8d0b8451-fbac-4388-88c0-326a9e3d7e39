#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : role_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""
# 标准库导入

# 第三方库导入
import django_filters
from django.apps import apps
from rest_framework import filters, mixins, viewsets

# 本地应用导入
from apps.user.filters import RoleFilter
from apps.user.serializers.role_serializer import (
    RoleCreateSerializer,
    RoleOutputSerializer,
    RoleUpdateSerializer,
)
from utils.ajax_result import AjaxResult
from utils.decorator import user_check
from utils.pagination import MyPageNumberPagination
from utils.user_check import is_admin


class RoleViewSet(
    viewsets.GenericViewSet,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    mixins.CreateModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
):
    """
    角色管理视图集

    提供系统角色的完整CRUD操作，包括角色的创建、查询、更新和删除功能。
    角色用于权限分组管理，可为角色分配权限，用户通过角色获得相应的系统访问权限。
    """

    queryset = (
        apps.get_model("auth", "Group")
        .objects.filter(name__startswith="r_")
        .prefetch_related("permissions")
    )

    filterset_class = RoleFilter
    pagination_class = MyPageNumberPagination
    filter_backends = [
        filters.OrderingFilter,
        django_filters.rest_framework.DjangoFilterBackend,
        filters.SearchFilter,
    ]

    ordering_fields = ["id", "name"]
    ordering = ["id"]
    search_fields = ["name"]

    def get_serializer_class(self):
        if self.action in ["create"]:
            return RoleCreateSerializer
        elif self.action in ["list", "retrieve"]:
            return RoleOutputSerializer
        elif self.action in ["update", "partial_update"]:
            return RoleUpdateSerializer
        return RoleOutputSerializer

    def list(self, request, *args, **kwargs):
        """
        获取角色列表

        查询系统中所有角色信息，支持分页查询、排序和搜索功能。
        返回角色的基本信息和权限配置，用于角色管理和用户角色分配。

        请求参数:
            查询参数:
                - page (int, 可选): 页码，默认为1
                - page_size (int, 可选): 每页数量，默认为10
                - ordering (str, 可选): 排序字段，可选值: "id", "name", "-id", "-name"
                - search (str, 可选): 搜索关键词，支持角色名称搜索

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "count": 总记录数,
                    "next": "下一页URL",
                    "previous": "上一页URL",
                    "results": [
                        {
                            "id": 角色ID,
                            "name": "角色名称",
                            "user_all_permissions": [权限详情列表],
                            "user_all_app": ["应用名称列表"]
                        }
                    ]
                }
            }
        """
        return super().list(request, *args, **kwargs)

    @user_check([is_admin])
    def create(self, request, *args, **kwargs):
        """
        创建新角色

        创建一个新的系统角色，可为角色分配权限用于用户权限管理。
        仅限管理员用户操作，创建成功后可为用户分配该角色。

        请求参数:
            请求体参数:
                - name (str, 必需): 角色名称
                - permissions (list, 可选): 角色权限ID列表

        请求数据示例:
            {
                "name": "编辑员",
                "permissions": [1, 2, 3, 5]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": null
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        self.perform_create(serializer)

        return AjaxResult.success()

    def retrieve(self, request, *args, **kwargs):
        """
        获取角色详细信息

        根据角色ID获取指定角色的详细信息，包括角色名称和权限配置。
        用于角色信息查看和编辑前的数据获取。

        请求参数:
            路径参数:
                - id (int, 必需): 角色ID

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "id": 角色ID,
                    "name": "角色名称",
                    "user_all_permissions": [权限详情列表],
                    "user_all_app": ["应用名称列表"]
                }
            }
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return AjaxResult.success(data=serializer.data)

    @user_check([is_admin])
    def update(self, request, *args, **kwargs):
        """
        更新角色信息

        更新指定角色的完整信息，包括角色名称和权限配置。
        仅限管理员用户操作，支持完整更新和部分更新。

        请求参数:
            路径参数:
                - id (int, 必需): 角色ID
            请求体参数:
                - name (str, 必需): 角色名称
                - permissions (list, 可选): 角色权限ID列表

        请求数据示例:
            {
                "name": "高级编辑员",
                "permissions": [1, 2, 3, 4, 5, 6]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return AjaxResult.success(msg="更新成功")

    @user_check([is_admin])
    def partial_update(self, request, *args, **kwargs):
        """
        部分更新角色信息

        部分更新指定角色的信息，只需提供需要修改的字段。
        仅限管理员用户操作，适用于单个或少数字段的修改场景。

        请求参数:
            路径参数:
                - id (int, 必需): 角色ID
            请求体参数:
                - name (str, 可选): 角色名称
                - permissions (list, 可选): 角色权限ID列表

        请求数据示例:
            {
                "name": "超级编辑员"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        kwargs["partial"] = True
        return self.update(request, *args, **kwargs)

    @user_check([is_admin])
    def destroy(self, request, *args, **kwargs):
        """
        删除角色

        删除指定的角色，删除后拥有该角色的用户将失去相应权限。
        仅限管理员用户操作，删除操作不可逆，请确保角色未被重要用户使用后再执行。

        请求参数:
            路径参数:
                - id (int, 必需): 要删除的角色ID

        响应数据结构:
            {
                "code": 200,
                "msg": "删除成功",
                "data": null
            }
        """
        instance = self.get_object()
        self.perform_destroy(instance)
        return AjaxResult.success(msg="删除成功")
