#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_serializer.py
<AUTHOR> AI Assistant
@Date     : 2025/07/23
@File_Desc: 微信小程序登录相关序列化器
"""

import logging

from django.contrib.auth import get_user_model
from django.db import transaction
from django.utils import timezone
from rest_framework import serializers

from apps.user.serializers.base import DynamicFieldsModelSerializer
from utils.wechat_api import get_wechat_api, WechatAPIError

User = get_user_model()
logger = logging.getLogger(__name__)


class WechatLoginSerializer(serializers.Serializer):
    """微信小程序登录序列化器"""

    js_code = serializers.CharField(max_length=64, required=True, help_text="小程序登录时获取的code")
    nickname = serializers.CharField(
        max_length=100, required=False, allow_blank=True, help_text="用户昵称（可选，用于首次登录时创建用户）"
    )
    avatar_url = serializers.URLField(
        required=False, allow_blank=True, help_text="用户头像URL（可选，用于首次登录时创建用户）"
    )

    def validate_js_code(self, value):
        """验证js_code格式"""
        if not value or len(value) < 10:
            raise serializers.ValidationError("无效的登录凭证")
        return value

    def validate_nickname(self, value):
        """验证昵称格式"""
        if value and len(value.strip()) > 100:
            raise serializers.ValidationError("昵称长度不能超过100个字符")
        return value.strip() if value else ""

    def validate_avatar_url(self, value):
        """验证头像URL格式"""
        if value and not value.strip():
            return ""
        return value

    def create(self, validated_data):
        """处理微信登录逻辑"""
        js_code = validated_data["js_code"]
        nickname = validated_data.get("nickname", "")
        avatar_url = validated_data.get("avatar_url", "")

        try:
            # 调用微信API获取session信息
            wechat_api = get_wechat_api()
            session_data = wechat_api.code2session(js_code)

            openid = session_data.get("openid")  # 微信小程序用户唯一标识（只针对当前的小程序有效）
            session_key = session_data.get("session_key")
            unionid = session_data.get("unionid")

            if not openid:  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
                raise serializers.ValidationError("获取微信用户信息失败")

            # 查找或创建用户
            user = self._get_or_create_user(
                openid, unionid, session_key, nickname, avatar_url
            )  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）

            return {
                "user": user,
                "openid": openid,  # 微信小程序用户唯一标识（只针对当前的小程序有效）
                "unionid": unionid,
                "session_key": session_key,
                "is_new_user": getattr(user, "_is_new_user", False),
            }

        except WechatAPIError as e:
            logger.error(f"微信登录失败: {e}")
            raise serializers.ValidationError(f"微信登录失败: {e.errmsg}")
        except Exception as e:
            logger.error(f"微信登录异常: {e}")
            raise serializers.ValidationError("登录服务暂时不可用，请稍后重试")

    def _get_or_create_user(
        self,
        openid: str,
        unionid: str,
        session_key: str,  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
        nickname: str,
        avatar_url: str,
    ) -> User:
        """获取或创建用户"""
        with transaction.atomic():
            # 首先尝试通过openid查找用户
            try:
                user = User.objects.get(
                    wechat_openid=openid
                )  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
                # 更新session_key和用户信息
                self._update_user_wechat_info(user, session_key, nickname, avatar_url)
                user._is_new_user = False
                return user
            except User.DoesNotExist:
                pass

            # 如果有unionid，尝试通过unionid查找用户
            if unionid:
                try:
                    user = User.objects.get(wechat_unionid=unionid)
                    # 绑定openid并更新信息
                    user.wechat_openid = openid  # 微信小程序用户唯一标识（只针对当前的小程序有效）
                    self._update_user_wechat_info(user, session_key, nickname, avatar_url)
                    user._is_new_user = False
                    return user
                except User.DoesNotExist:
                    pass

            # 创建新用户
            user = self._create_new_user(openid, unionid, session_key, nickname, avatar_url)
            user._is_new_user = True
            return user

    def _update_user_wechat_info(self, user: User, session_key: str, nickname: str, avatar_url: str) -> None:
        """更新用户微信信息"""
        user.wechat_session_key = session_key
        user.wechat_bind_time = timezone.now()

        # 更新用户信息（如果提供）
        if nickname:
            user.wechat_nickname = nickname
        if avatar_url:
            user.wechat_avatar_url = avatar_url

        user.save()

    def _create_new_user(
        self,
        openid: str,
        unionid: str,
        session_key: str,  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
        nickname: str,
        avatar_url: str,
    ) -> User:
        """创建新用户"""
        # 生成用户名（使用完整的openid）
        username = f"wx_{openid}"  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）

        # 确保用户名唯一
        counter = 1
        original_username = username
        while User.objects.filter(username=username).exists():
            username = f"{original_username}_{counter}"
            counter += 1

        # 创建用户
        user = User.objects.create(
            username=username,
            wechat_openid=openid,  # 微信小程序用户唯一标识（只针对当前的小程序有效）
            wechat_unionid=unionid,
            wechat_session_key=session_key,
            wechat_nickname=nickname,
            wechat_avatar_url=avatar_url,
            wechat_bind_time=timezone.now(),
            is_active=True,
        )

        logger.info(
            f"创建新的微信用户: {username} (openid: {openid})"
        )  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
        return user


class WechatBindSerializer(serializers.Serializer):
    """微信账号绑定序列化器"""

    js_code = serializers.CharField(max_length=64, required=True, help_text="小程序登录时获取的code")

    def validate_js_code(self, value):
        """验证js_code格式"""
        if not value or len(value) < 10:
            raise serializers.ValidationError("无效的登录凭证")
        return value

    def create(self, validated_data):
        """处理微信账号绑定逻辑"""
        js_code = validated_data["js_code"]
        user = self.context["request"].user

        if not user.is_authenticated:
            raise serializers.ValidationError("用户未登录")

        if user.is_wechat_bound():
            raise serializers.ValidationError("该账号已绑定微信")

        try:
            # 调用微信API获取session信息
            wechat_api = get_wechat_api()
            session_data = wechat_api.code2session(js_code)

            openid = session_data.get("openid")  # 微信小程序用户唯一标识（只针对当前的小程序有效）
            session_key = session_data.get("session_key")
            unionid = session_data.get("unionid")

            if not openid:  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
                raise serializers.ValidationError("获取微信用户信息失败")

            # 检查openid是否已被其他用户绑定
            if (
                User.objects.filter(wechat_openid=openid).exclude(id=user.id).exists()
            ):  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
                raise serializers.ValidationError("该微信账号已被其他用户绑定")

            # 绑定微信账号
            with transaction.atomic():
                user.bind_wechat(
                    openid=openid,  # 微信小程序用户唯一标识（只针对当前的小程序有效）
                    unionid=unionid,
                    session_key=session_key,
                )

            logger.info(
                f"用户 {user.username} 成功绑定微信账号 (openid: {openid})"
            )  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）

            return {
                "user": user,
                "openid": openid,  # 微信小程序用户唯一标识（只针对当前的小程序有效）
                "unionid": unionid,
                "bind_time": user.wechat_bind_time,
            }

        except WechatAPIError as e:
            logger.error(f"微信绑定失败: {e}")
            raise serializers.ValidationError(f"微信绑定失败: {e.errmsg}")
        except Exception as e:
            logger.error(f"微信绑定异常: {e}")
            raise serializers.ValidationError("绑定服务暂时不可用，请稍后重试")


class WechatStatusSerializer(DynamicFieldsModelSerializer):
    """微信绑定状态序列化器"""

    is_wechat_bound = serializers.SerializerMethodField()
    wechat_nickname = serializers.CharField(read_only=True)
    wechat_avatar_url = serializers.URLField(read_only=True)
    wechat_bind_time = serializers.DateTimeField(read_only=True, format="%Y-%m-%d %H:%M:%S")

    class Meta:
        model = User
        fields = (
            "id",
            "username",
            "is_wechat_bound",
            "wechat_nickname",
            "wechat_avatar_url",
            "wechat_bind_time",
        )

    def get_is_wechat_bound(self, obj):
        """获取微信绑定状态"""
        return obj.is_wechat_bound()


class WechatSessionRefreshSerializer(serializers.Serializer):
    """微信session_key刷新序列化器"""

    def create(self, validated_data):
        """刷新用户的session_key"""
        user = self.context["request"].user

        if not user.is_authenticated:
            raise serializers.ValidationError("用户未登录")

        if not user.is_wechat_bound():
            raise serializers.ValidationError("用户未绑定微信账号")

        try:
            wechat_api = get_wechat_api()

            # 检查当前session_key是否有效
            is_valid = wechat_api.check_session_key(user.wechat_openid, user.wechat_session_key)

            if is_valid:
                return {"status": "valid", "message": "session_key仍然有效", "session_key_valid": True}

            # session_key已失效，尝试重置
            new_session_key = wechat_api.reset_user_session_key(user.wechat_openid, user.wechat_session_key)

            if new_session_key:
                # 更新用户的session_key
                with transaction.atomic():
                    user.wechat_session_key = new_session_key
                    user.save()

                logger.info(f"用户 {user.username} 的session_key已刷新")

                return {"status": "refreshed", "message": "session_key已刷新", "session_key_valid": True}
            else:
                raise serializers.ValidationError("session_key刷新失败")

        except WechatAPIError as e:
            logger.error(f"session_key刷新失败: {e}")
            raise serializers.ValidationError(f"session_key刷新失败: {e.errmsg}")
        except Exception as e:
            logger.error(f"session_key刷新异常: {e}")
            raise serializers.ValidationError("刷新服务暂时不可用，请稍后重试")
