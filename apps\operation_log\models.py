from django.db import models
from authsage.base_model import BaseModel


class LoginLog(BaseModel):
    username = models.Char<PERSON><PERSON>(max_length=255, help_text="操作人")
    department = models.CharField(max_length=255, help_text="部门")
    account = models.CharField(max_length=255, help_text="账号")
    ip = models.CharField(max_length=50, help_text="登录IP")
    login_time = models.DateTimeField(auto_now_add=True, help_text="登录时间")
    logout_time = models.DateTimeField(null=True, blank=True, help_text="退出登录时间")
    login_type = models.Char<PERSON>ield(
        max_length=50,
        default="password",
        help_text="登录类型：password(密码登录)、wechat_miniprogram(微信小程序登录)"
    )

    class Meta:
        db_table = "t_login_log"
        verbose_name = "登录日志表"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.username


class OperationLog(BaseModel):
    username = models.CharField(max_length=255, help_text="操作人")
    account = models.CharField(max_length=255, help_text="账号")
    button = models.CharField(max_length=255, help_text="功能按钮名称")
    kind = models.CharField(max_length=255, help_text="功能按钮类型")
    url = models.TextField(help_text="操作URL")
    belong = models.CharField(max_length=255, help_text="业务所属模块")
    ip = models.CharField(max_length=50, help_text="所在IP")

    class Meta:
        db_table = "t_operation_log"
        verbose_name = "操作日志表"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.username
