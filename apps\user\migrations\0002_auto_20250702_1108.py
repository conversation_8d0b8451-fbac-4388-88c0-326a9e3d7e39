# Generated by Django 4.1.13 on 2025-07-02 11:08

from django.db import migrations, transaction
import pandas as pd
from oauth2_provider.generators import generate_client_id, generate_client_secret
from oauth2_provider.models import get_application_model

NEED_CREATE_APPLICATION_LIST = ["智能处置运营管理"]


def insert_application_data(apps, schema_editor):
    Application = get_application_model()

    # 批量创建应用的基础数据
    base_data = {
        "redirect_uris": "",
        "client_type": "confidential",
        "authorization_grant_type": "password",
    }

    for name in NEED_CREATE_APPLICATION_LIST:
        application_dict = {
            "name": name,
            "client_id": generate_client_id(),
            "client_secret": generate_client_secret(),
        }
        application_dict.update(base_data)
        for key, value in application_dict.items():
            print(f"\n{key}: {value}")
        Application.objects.create(**application_dict)


def reverse_application_data(apps, schema_editor):
    get_application_model().objects.filter(
        name__in=NEED_CREATE_APPLICATION_LIST
    ).delete()


def adjust_permission_data_sql(apps, schema_editor):
    try:
        excel_data = pd.read_excel("documents/菜单功能清单调整20250702.xlsx")
    except FileNotFoundError:
        raise Exception("权限配置文件未找到，请确认文件路径是否正确")

    # 获取所有应用ID
    Application = get_application_model()
    app_names = excel_data["应用名称"].unique()
    applications = {
        app.name: app.id for app in Application.objects.filter(name__in=app_names)
    }

    # 获取content_type
    ContentType = apps.get_model("contenttypes", "ContentType")
    content_types = {}

    # 预处理填充NA值
    excel_data.fillna("", inplace=True)

    with transaction.atomic():
        with schema_editor.connection.cursor() as cursor:
            for _, row in excel_data.iterrows():
                app_name = row["应用名称"]
                app_id = applications.get(app_name)

                if not app_id:
                    print(f"应用 '{app_name}' 不存在，跳过相关权限配置")
                    continue

                # 获取或创建content_type
                if app_name not in content_types:
                    content_type = ContentType.objects.filter(
                        app_label=app_name, model=app_name
                    ).first()
                    if not content_type:
                        content_type = ContentType.objects.create(
                            app_label=app_name, model=app_name
                        )
                    content_types[app_name] = content_type.id

                content_type_id = content_types[app_name]

                if row["动作"] == "更改":
                    # 使用原生SQL更新权限
                    query = f"""
                    UPDATE auth_permission 
                    SET name = '{row["名称"]}', codename = '{row["编码名称"]}' 
                    WHERE codename = '{row["原编码名称"]}'
                    """
                    cursor.execute(query)

                elif row["动作"] == "新增":
                    # 处理父级权限
                    parent_id = "NULL"
                    if row["父级名称"]:
                        # 查询父级权限ID
                        parent_query = f"SELECT id FROM auth_permission WHERE name = '{row['父级名称']}' AND application_id = '{app_id}'"
                        cursor.execute(parent_query)
                        parent_result = cursor.fetchone()

                        if parent_result:
                            parent_id = parent_result[0]
                        else:
                            # 创建父级权限
                            parent_insert_query = f"""
                            INSERT INTO auth_permission 
                            (name, codename, permission_type, content_type_id, application_id) 
                            VALUES ('{row["父级名称"]}', '{row["父级名称"]}', 'menu', '{content_type_id}', '{app_id}')
                            """
                            cursor.execute(parent_insert_query)

                            # 获取新创建的父级权限ID
                            cursor.execute("SELECT LAST_INSERT_ID()")
                            parent_id = cursor.fetchone()[0]

                    # 插入新权限
                    permission_query = f"""
                    INSERT INTO auth_permission 
                    (name, codename, parent_id, permission_type, content_type_id, application_id) 
                    VALUES ('{row["名称"]}', '{row["编码名称"]}', {parent_id}, '{row["菜单/功能"]}', '{content_type_id}', '{app_id}')
                    """
                    cursor.execute(permission_query)


class Migration(migrations.Migration):

    dependencies = [
        ("user", "0001_initial"),
    ]

    operations = [
        migrations.RunPython(insert_application_data, reverse_application_data),
        migrations.RunPython(adjust_permission_data_sql, migrations.RunPython.noop),
    ]
