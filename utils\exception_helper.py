#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : exception_helper.py
<AUTHOR> JT_DA
@Date     : 2025/04/15
@File_Desc:
"""
from rest_framework.views import exception_handler
from rest_framework.response import Response


def custom_exception_handler(exc, context):
    # 先调用默认的异常处理器获取标准的响应
    response = exception_handler(exc, context)

    if response is not None:
        # 在标准响应的基础上自定义返回内容
        custom_data = {"code": response.status_code, "msg": exc.detail, "state": "fail"}
        response.data = custom_data
        # 使用Response对象返回自定义响应
        return Response(custom_data, status=response.status_code)

    return response
