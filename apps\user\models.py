from django.contrib.auth.models import AbstractUser
from django.contrib.auth.models import Permission, Group
from django.db import models
from django.utils import timezone


class User(AbstractUser):
    created_time = models.DateTimeField(auto_now_add=True, verbose_name="创建日期")
    updated_time = models.DateTimeField(auto_now=True, verbose_name="更新日期")
    phone_number = models.CharField(max_length=11, blank=True, null=True, verbose_name="手机号码")
    id_card_number = models.CharField(max_length=18, blank=True, null=True, verbose_name="身份证号码")

    # 人脸核身相关字段
    real_name = models.CharField(
        max_length=100, blank=True, null=True, verbose_name="真实姓名", help_text="用户的真实姓名，通过人脸核身验证获得"
    )
    biz_token = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name="人脸核身业务标识",
        help_text="人脸核身流程的唯一业务标识，用于追踪核身流程",
    )
    detect_auth_time = models.DateTimeField(
        blank=True, null=True, verbose_name="人脸核身时间", help_text="人脸核身完成的日期时间"
    )
    detect_auth_result = models.BooleanField(
        default=False,
        null=True,
        blank=True,
        verbose_name="人脸核身结果",
        help_text="人脸核身结果：True表示核身成功，False表示核身失败",
    )

    # 微信小程序相关字段
    wechat_openid = models.CharField(  # 微信小程序用户唯一标识（只针对当前的小程序有效）
        max_length=64, unique=True, null=True, blank=True, verbose_name="微信OpenID", help_text="微信小程序用户唯一标识"
    )
    wechat_unionid = models.CharField(
        max_length=64, null=True, blank=True, verbose_name="微信UnionID", help_text="微信开放平台用户唯一标识"
    )
    wechat_session_key = models.CharField(
        max_length=128, null=True, blank=True, verbose_name="微信SessionKey", help_text="微信会话密钥(加密存储)"
    )
    wechat_nickname = models.CharField(max_length=64, null=True, blank=True, verbose_name="微信昵称")
    wechat_avatar_url = models.URLField(null=True, blank=True, verbose_name="微信头像URL")
    wechat_bind_time = models.DateTimeField(null=True, blank=True, verbose_name="微信绑定时间")

    class Meta:
        db_table = "auth_user"
        verbose_name = "用户信息表"
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.username

    def is_wechat_bound(self):
        """检查是否已绑定微信"""
        return bool(self.wechat_openid)

    def bind_wechat(
        self, openid, unionid=None, session_key=None, nickname=None, avatar_url=None
    ):  # openid: 微信小程序用户唯一标识（只针对当前的小程序有效）
        """绑定微信账号"""
        self.wechat_openid = openid  # 微信小程序用户唯一标识（只针对当前的小程序有效）
        self.wechat_unionid = unionid
        self.wechat_session_key = session_key
        self.wechat_nickname = nickname
        self.wechat_avatar_url = avatar_url
        self.wechat_bind_time = timezone.now()
        self.save()

    def unbind_wechat(self):
        """解绑微信账号"""
        self.wechat_openid = None  # 微信小程序用户唯一标识（只针对当前的小程序有效）
        self.wechat_unionid = None
        self.wechat_session_key = None
        self.wechat_nickname = None
        self.wechat_avatar_url = None
        self.wechat_bind_time = None
        self.save()


Permission.add_to_class("permission_type", models.CharField(max_length=180, null=True, blank=True))
Permission.add_to_class("parent_id", models.PositiveIntegerField(null=True, blank=True))
Permission.add_to_class("application_id", models.PositiveIntegerField(null=True, blank=True))


class WechatMiniProgramConfig(models.Model):
    """微信小程序配置模型"""

    app_name = models.CharField(max_length=100, verbose_name="应用名称", help_text="微信小程序应用名称")
    app_id = models.CharField(max_length=64, unique=True, verbose_name="AppID", help_text="微信小程序AppID")
    app_secret = models.CharField(max_length=64, verbose_name="AppSecret", help_text="微信小程序AppSecret")
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_time = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_time = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    description = models.TextField(blank=True, null=True, verbose_name="描述信息")

    class Meta:
        db_table = "wechat_miniprogram_config"
        verbose_name = "微信小程序配置"
        verbose_name_plural = verbose_name
        ordering = ["-created_time"]

    def __str__(self):
        return f"{self.app_name}({self.app_id})"


# 迁移
# python manage.py makemigrations
# python manage.py migrate
