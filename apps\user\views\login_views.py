#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : login_views.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""

# 标准库导入


# 第三方库导入
from django.conf import settings
from django.http import HttpResponse
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import AllowAny
from ipware import get_client_ip

# 本地应用导入
from apps.user.serializers.user_serializer import UserOutputSerializer
from apps.user.serializers.set_password_serializer import SetPasswordSerializer
from apps.user.serializers.reset_password_serializer import ResetPasswordSerializer
from apps.user.serializers.login_serializer import (
    RefreshSerializer,
    UserLoginSerializer,
    UserLogoutSerializer,
)
from apps.user.utils.captcha import Captcha
from apps.user.models import User
from apps.operation_log.models import LoginLog
from utils.ajax_result import AjaxResult
from utils.token import get_auth_token, refresh_token, revoke_token


class LoginView(GenericAPIView):
    """
    用户登录接口

    提供用户名密码登录功能，支持验证码验证和OAuth2令牌生成。
    登录成功后返回访问令牌和用户信息，并记录登录日志。
    """

    authentication_classes = []
    permission_classes = [AllowAny]  # 允许任何用户访问
    serializer_class = UserLoginSerializer

    def post(self, request):
        """
        用户登录认证

        使用用户名、密码和验证码进行用户身份认证，验证成功后生成OAuth2访问令牌。
        支持管理员和普通用户登录，根据用户类型返回不同的重定向URL。

        请求参数:
            请求体参数:
                - username (str, 必需): 用户名
                - password (str, 必需): 用户密码
                - captcha (str, 必需): 验证码，4位字符
                - client_id (str, 可选): OAuth2客户端ID
                - client_secret (str, 可选): OAuth2客户端密钥

        请求数据示例:
            {
                "username": "admin",
                "password": "password123",
                "captcha": "ABCD"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "access_token": "访问令牌",
                    "refresh_token": "刷新令牌",
                    "token_type": "Bearer",
                    "expires_in": 令牌过期时间(秒),
                    "redirect_url": "重定向URL"
                }
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        username = serializer.data.get("username")
        password = serializer.data.get("password")
        # client_id = serializer.data.get("client_id")
        # client_secret = serializer.data.get("client_secret")
        captcha = serializer.data.get("captcha")
        # 解密处理
        # sm2_crypt = sm2.CryptSM2(public_key=settings.PUBLIC_KEY, private_key=settings.PRIVATE_KEY, mode=1)
        # password = sm2_crypt.decrypt(bytes.fromhex(password)).decode('utf8')

        # 验证码校验
        captcha_text = request.session.get("captcha")
        if captcha.lower() != captcha_text.lower():
            return AjaxResult.fail(msg="验证码错误")

        # 构建认证请求数据
        token_dict = {
            "grant_type": "password",
            "username": username,
            "password": password,
            "client_id": settings.CLIENT_ID,
            "client_secret": settings.CLIENT_SECRET,
        }
        content = get_auth_token(**token_dict)

        if "error" not in content:
            user = User.objects.get(username=username)
            if not user.is_superuser:
                # 非超级管理员，跳转到运营管理前端
                content["redirect_url"] = settings.OPS_MANAGEMENT_HTM_FRONTEND_URL
                client_ip, _ = get_client_ip(request)
                serializer = UserOutputSerializer(user)
                # 登录成功后生成登录日志
                LoginLog.objects.create(
                    username=username,
                    department=serializer.data.get("department")[0].get("name"),
                    account=username,
                    ip=client_ip,
                )
            return AjaxResult.success(data=content)
        else:
            return AjaxResult.fail(msg="登录失败")


class CaptchaView(GenericAPIView):
    """
    验证码生成接口

    生成图形验证码用于登录验证，提高系统安全性防止自动化攻击。
    验证码为4位随机字符，存储在用户会话中用于后续验证。
    """

    authentication_classes = []
    permission_classes = [AllowAny]  # 允许任何用户访问

    def get(self, request):
        """
        生成图形验证码

        生成一个4位字符的图形验证码，验证码文本存储在session中。
        返回PNG格式的验证码图片，用于用户登录时的安全验证。

        请求参数:
            无需参数

        响应数据结构:
            返回PNG格式的图片数据，Content-Type为image/png
        """
        captcha = Captcha()
        text, image = captcha.generate_captcha()
        request.session["captcha"] = text
        return HttpResponse(image, content_type="image/png")


class RefreshView(GenericAPIView):
    """
    访问令牌刷新接口

    使用刷新令牌获取新的访问令牌，延长用户会话有效期。
    当访问令牌过期时，客户端可使用此接口获取新令牌而无需重新登录。
    """

    serializer_class = RefreshSerializer
    permission_classes = [AllowAny]

    def post(self, request):
        """
        刷新OAuth2访问令牌

        使用有效的刷新令牌获取新的访问令牌和刷新令牌。
        适用于访问令牌过期但刷新令牌仍有效的场景。

        请求参数:
            请求体参数:
                - refresh_token (str, 必需): 有效的刷新令牌
                - client_id (str, 必需): OAuth2客户端ID
                - client_secret (str, 必需): OAuth2客户端密钥

        请求数据示例:
            {
                "refresh_token": "your_refresh_token_here",
                "client_id": "your_client_id",
                "client_secret": "your_client_secret"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "access_token": "新的访问令牌",
                    "refresh_token": "新的刷新令牌",
                    "token_type": "Bearer",
                    "expires_in": 令牌过期时间(秒)
                }
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        client_id = serializer.data.get("client_id")
        client_secret = serializer.data.get("client_secret")
        _refresh_token = serializer.data.get("refresh_token")

        # 构建认证请求数据 - 使用优化后的refresh_token函数
        content = refresh_token(
            client_id=client_id,
            client_secret=client_secret,
            refresh_token_value=_refresh_token
        )

        return AjaxResult.success(data=content)


class LogoutView(GenericAPIView):
    """
    用户注销接口

    撤销用户的访问令牌和刷新令牌，安全退出系统。
    注销后令牌将失效，用户需要重新登录才能访问受保护的资源。
    """

    serializer_class = UserLogoutSerializer

    def post(self, request):
        """
        用户注销登录

        撤销指定的访问令牌或刷新令牌，使其立即失效。
        支持撤销访问令牌和刷新令牌，确保用户安全退出。

        请求参数:
            请求体参数:
                - token (str, 必需): 要撤销的令牌（访问令牌或刷新令牌）
                - token_type (str, 可选): 令牌类型，可选值: "access_token", "refresh_token"

        请求数据示例:
            {
                "token": "your_access_token_here",
                "token_type": "access_token"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "退出成功",
                "data": {}
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        token = serializer.data.get("token")
        token_type_hint = serializer.data.get("token_type")
        # client_id = serializer.data.get("client_id")
        # client_secret = serializer.data.get("client_secret")

        # 构建认证请求数据
        token_request = {
            "client_id": settings.CLIENT_ID,
            "client_secret": settings.CLIENT_SECRET,
            "token": token,
            "token_type_hint": token_type_hint,
        }
        content = revoke_token(**token_request)

        return AjaxResult.success(msg="退出成功", data=content)


class SetPasswordView(GenericAPIView):
    """
    用户密码修改接口

    允许已登录用户修改自己的密码，需要验证原密码确保安全性。
    修改成功后用户需要使用新密码重新登录。
    """

    serializer_class = SetPasswordSerializer

    def post(self, request):
        """
        修改用户密码

        验证用户原密码后设置新密码，确保密码修改的安全性。
        要求新密码确认一致，修改成功后建议用户重新登录。

        请求参数:
            请求体参数:
                - old_password (str, 必需): 原密码
                - password (str, 必需): 新密码
                - new_password (str, 必需): 新密码确认

        请求数据示例:
            {
                "old_password": "old_password123",
                "password": "new_password456",
                "new_password": "new_password456"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "修改成功",
                "data": null
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        password = serializer.data.get("password")
        old_password = serializer.data.get("old_password")
        new_password = serializer.data.get("new_password")

        # 必须包含大小写字母、数字和特殊字符，至少8个字符
        # regex = '^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$'
        # if not re.search(regex, password):
        #     return AjaxResult.fail(msg="密码格式不正确")

        if password != new_password:
            return AjaxResult.fail(msg="密码不一致")

        # 校验旧密码
        user = request.user
        if not user.check_password(old_password):
            return AjaxResult.fail(msg="旧密码错误")

        # 修改密码
        user.set_password(new_password)
        user.save()

        return AjaxResult.success(msg="修改成功")


class ResetPasswordView(GenericAPIView):
    """
    用户密码重置接口

    管理员功能，将指定用户的密码重置为系统默认密码。
    用于用户忘记密码或管理员需要重置用户密码的场景。
    """

    serializer_class = ResetPasswordSerializer

    def post(self, request):
        """
        重置用户密码为默认密码

        将指定用户名的密码重置为系统默认密码，用户可使用默认密码登录后自行修改。
        通常用于用户忘记密码或管理员批量重置密码的场景。

        请求参数:
            请求体参数:
                - username (str, 必需): 要重置密码的用户名

        请求数据示例:
            {
                "username": "user123"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "密码重置成功",
                "data": null
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        username = serializer.data.get("username")

        try:
            user = User.objects.get(username=username)
        except User.DoesNotExist:
            return AjaxResult.fail(msg="用户不存在")

        # 重置为默认密码
        default_password = "A12345678a."
        user.set_password(default_password)
        user.save()

        return AjaxResult.success(msg="密码重置成功")
