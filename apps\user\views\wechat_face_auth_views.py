#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_face_auth_views.py
<AUTHOR> AI Assistant
@Date     : 2025/07/24
@File_Desc: 微信用户人脸核身相关视图
"""

import logging

from django.contrib.auth import get_user_model
from django.db import transaction
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated

from apps.user.serializers.wechat_face_auth_serializer import WechatFaceAuthUpdateSerializer
from utils.ajax_result import AjaxResult

User = get_user_model()
logger = logging.getLogger(__name__)


class WechatFaceAuthUpdateView(GenericAPIView):
    """
    微信用户人脸核身信息更新接口

    处理微信用户经人脸核身后的用户信息更新。通过Authorization令牌获取当前用户，
    根据提供的人脸核身相关参数更新用户信息。所有参数均为可选，支持部分更新。
    仅支持POST方法进行信息更新，用户信息查询请使用 /info/ 端点。
    """

    permission_classes = [IsAuthenticated]
    serializer_class = WechatFaceAuthUpdateSerializer

    def post(self, request):
        """
        更新用户人脸核身信息

        接收微信用户人脸核身后的相关信息，更新当前登录用户的人脸核身相关字段。
        用户通过Authorization请求头中的OAuth2令牌进行身份验证。

        请求参数:
            请求头参数:
                - Authorization (str, 必需): Bearer token格式的OAuth2访问令牌

            请求体参数:
                - real_name (str, 可选): 用户的真实姓名，通过人脸核身验证获得
                - id_card_number (str, 可选): 用户的身份证号码，支持15位或18位格式
                - biz_token (str, 可选): 人脸核身流程的唯一业务标识，用于追踪核身流程
                - detect_auth_time (datetime, 可选): 人脸核身完成的日期时间，ISO格式
                - detect_auth_result (bool, 可选): 人脸核身结果，true表示成功，false表示失败

        请求数据示例:
            {
                "real_name": "张三",
                "id_card_number": "110101199001011234",
                "biz_token": "wx_face_auth_20250724_123456",
                "detect_auth_time": "2025-07-24T10:30:00Z",
                "detect_auth_result": true
            }

        响应数据结构:
            成功响应:
            {
                "code": 200,
                "msg": "人脸核身信息更新成功"
            }

            错误响应:
            {
                "code": 400,
                "msg": "参数验证失败",
                "data": {
                    "id_card_number": ["身份证号码格式不正确"]
                }
            }
        """
        try:
            # 获取当前登录用户
            user = request.user

            # 记录请求日志
            logger.info(f"用户 {user.username} 请求更新人脸核身信息，" f"请求数据: {request.data}")

            # 验证请求数据
            serializer = self.get_serializer(data=request.data)
            if not serializer.is_valid():
                logger.warning(f"用户 {user.username} 人脸核身信息更新失败，" f"参数验证错误: {serializer.errors}")
                return AjaxResult.error(msg="参数验证失败", data=serializer.errors, code=status.HTTP_400_BAD_REQUEST)

            # 使用数据库事务确保数据一致性
            with transaction.atomic():
                # 更新用户人脸核身信息
                serializer.update_user_face_auth_info(user, serializer.validated_data)

                # 记录成功日志
                logger.info(
                    f"用户 {user.username} 人脸核身信息更新成功，" f"更新字段: {list(serializer.validated_data.keys())}"
                )

                return AjaxResult.success(msg="人脸核身信息更新成功")

        except Exception as e:
            # 记录异常日志
            logger.error(
                f"用户 {request.user.username if request.user.is_authenticated else 'Anonymous'} "
                f"人脸核身信息更新异常: {str(e)}",
                exc_info=True,
            )

            return AjaxResult.error(msg="系统内部错误，请稍后重试", code=status.HTTP_500_INTERNAL_SERVER_ERROR)
