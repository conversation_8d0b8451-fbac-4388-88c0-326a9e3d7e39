import django_filters
from rest_framework import filters, mixins, viewsets

from apps.operation_log.filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OperationLogFilter
from apps.operation_log.models import LoginLog, OperationLog
from apps.operation_log.serializers import LoginLogSerializer, OperationLogSerializer
from utils.pagination import MyPageNumberPagination


class OperationLogViewSet(
    viewsets.GenericViewSet,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    mixins.DestroyModelMixin,
    mixins.CreateModelMixin,
):
    """操作日志"""

    queryset = OperationLog.objects.all()

    serializer_class = OperationLogSerializer
    filterset_class = OperationLogFilter

    pagination_class = MyPageNumberPagination

    filter_backends = [
        filters.OrderingFilter,
        django_filters.rest_framework.DjangoFilterBackend,
    ]

    ordering_fields = ["id", "created", "logout_time"]
    ordering = ["id"]

    def list(self, request, *args, **kwargs):
        """日志列表"""
        return super().list(request, *args, **kwargs)


class LoginLogViewSet(
    viewsets.GenericViewSet,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    mixins.DestroyModelMixin,
    mixins.CreateModelMixin,
):
    """登录日志"""

    queryset = LoginLog.objects.all()

    serializer_class = LoginLogSerializer
    filterset_class = LoginLogFilter

    pagination_class = MyPageNumberPagination

    filter_backends = [
        filters.OrderingFilter,
        django_filters.rest_framework.DjangoFilterBackend,
    ]

    ordering_fields = ["id", "login_time", "logout_time"]
    ordering = ["id"]

    def list(self, request, *args, **kwargs):
        """日志列表"""
        return super().list(request, *args, **kwargs)
