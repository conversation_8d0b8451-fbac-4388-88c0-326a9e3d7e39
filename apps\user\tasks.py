#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : tasks.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""

from django.apps import apps

from apps.user.serializers.permission_serializer import PermissionSerializer
from authsage.celery import app
from authsage.constant import RedisKey
from utils.cache_manager import CacheManager


@app.task()
def check_application(application_id):
    """
    检测application是否存在

    :param application_id: 应用id
    :return: 应用对象，不存在返回None
    """
    Application = apps.get_model("oauth2_provider", "Application")

    try:
        application = Application.objects.get(id=application_id)
    except Application.DoesNotExist:
        return None

    return application


# 前置部门检测
@app.task()
def check_department_before(department_name):
    """
    检测department的前置部门是否存在

    :param department_name: 部门名称
    :return: 前置部门对象，不存在返回None
    """
    Group = apps.get_model("auth", "Group")
    last_hyphen_index = department_name.rfind("-")
    if last_hyphen_index > -1:
        before_last_hyphen = department_name[:last_hyphen_index]
        before_department = Group.objects.filter(name=before_last_hyphen)
        if before_department.exists():
            return before_department.first()
    return None


# 部门或这角色存在与否检测
@app.task()
def check_dr_exist(dr_name):
    """
    检测部门或角色是否存在

    :param dr_name: 部门或角色名称
    :return: 部门或角色对象，不存在返回None
    """
    Group = apps.get_model("auth", "Group")
    if Group.objects.filter(name=dr_name).exists():
        return Group.objects.get(name=dr_name)
    return None


# 将权限id转换为权限对象，形式为字典
@app.task()
def permission_id_to_permission(permission_id):
    """
    将权限id转换为权限对象，形式为字典

    :param permission_id: 权限id
    :return: 权限对象，不存在返回None
    """

    # 首先从缓存中获取权限对象
    cache = CacheManager(RedisKey.PERMISSION)
    permission = cache.get(permission_id)
    if permission:
        return PermissionSerializer(permission).data
    else:
        Permission = apps.get_model("auth", "Permission")
        if Permission.objects.filter(id=permission_id).exists():
            permission = Permission.objects.get(id=permission_id)
            cache.set(permission_id, permission)
            return PermissionSerializer(permission).data
    return None


###################################操作Application模型###################################


@app.task()
def update_application_cache_by_id(application_id):
    """
    通过id更新application缓存

    :param application_id: 应用id
    :return: None
    """
    application_cache = CacheManager(RedisKey.APPLICATION)
    application_cache.delete(application_id)
    try:
        Application = apps.get_model("oauth2_provider", "Application")
        application = Application.objects.get(id=application_id)
        application_cache.set(application_id, application)
    except Application.DoesNotExist:
        return False

    return True


@app.task()
def get_application_by_id(application_id):
    """
    通过id获取application对象

    :param application_id: 应用id
    :return: 应用对象，不存在返回None
    """
    application_cache = CacheManager(RedisKey.APPLICATION)
    application = application_cache.get(application_id)
    if application:
        return application
    else:
        update_application_cache_by_id.delay(application_id)

    return apps.get_model("oauth2_provider", "Application").objects.get(
        id=application_id
    )
