#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_login_views.py
<AUTHOR> AI Assistant
@Date     : 2025/07/23
@File_Desc: 微信小程序登录相关视图
"""

import logging

from django.conf import settings
from django.contrib.auth import get_user_model
from django.db import transaction
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import AllowAny, IsAuthenticated

from apps.operation_log.models import LoginLog
from apps.user.serializers.user_serializer import UserOutputSerializer
from apps.user.serializers.wechat_serializer import (
    WechatLoginSerializer,
    WechatBindSerializer,
    WechatStatusSerializer,
    WechatSessionRefreshSerializer,
)
from utils.ajax_result import AjaxResult
from utils.token import get_auth_token
from utils.wechat_api import get_wechat_api
from ipware import get_client_ip

User = get_user_model()
logger = logging.getLogger(__name__)


class WechatLoginView(GenericAPIView):
    """
    微信小程序登录接口

    提供微信小程序用户的登录功能，支持新用户自动注册和老用户直接登录。
    通过微信授权码获取用户信息，生成系统访问令牌，并记录登录日志。
    """

    authentication_classes = []
    permission_classes = [AllowAny]
    serializer_class = WechatLoginSerializer

    def post(self, request):
        """
        微信小程序用户登录

        使用微信小程序授权码进行用户身份认证，支持新用户自动注册。
        登录成功后返回OAuth2访问令牌和用户信息，用于后续API调用。

        请求参数:
            请求体参数:
                - js_code (str, 必需): 微信小程序登录时获取的授权码
                - nickname (str, 可选): 用户昵称
                - avatar_url (str, 可选): 用户头像URL

        请求数据示例:
            {
                "js_code": "0a1b2c3d4e5f6g7h8i9j",
                "nickname": "微信用户",
                "avatar_url": "https://wx.qlogo.cn/mmopen/..."
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "微信登录成功",
                "data": {
                    "access_token": "访问令牌",
                    "refresh_token": "刷新令牌",
                    "token_type": "Bearer",
                    "expires_in": 令牌过期时间(秒),
                    "user_info": {用户详细信息},
                    "is_new_user": true/false,
                    "openid": "微信用户唯一标识",
                    "login_type": "wechat_miniprogram"
                }
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            # 处理微信登录
            login_result = serializer.save()
            user = login_result["user"]
            is_new_user = login_result["is_new_user"]

            # 生成OAuth2令牌
            token_dict = {
                "grant_type": "password",
                "username": user.username,
                "password": user.username,  # 微信用户使用username作为密码
                "client_id": settings.CLIENT_ID,
                "client_secret": settings.CLIENT_SECRET,
            }

            # 为微信用户设置临时密码（如果是新用户）
            if is_new_user:
                user.set_password(user.username)
                user.save()

            # 获取OAuth2令牌
            token_content = get_auth_token(**token_dict)

            if "error" not in token_content:
                # 获取客户端IP
                client_ip, _ = get_client_ip(request)

                # 序列化用户信息
                user_serializer = UserOutputSerializer(user)

                # 记录登录日志
                try:
                    department_name = ""
                    if user_serializer.data.get("department"):
                        department_name = user_serializer.data["department"][0].get("name", "")

                    LoginLog.objects.create(
                        username=user.username,
                        department=department_name,
                        account=user.username,
                        ip=client_ip,
                        login_type="wechat_miniprogram",  # 标识微信登录
                    )
                except Exception as e:
                    logger.warning(f"记录微信登录日志失败: {e}")

                # 构建响应数据
                response_data = {
                    "access_token": token_content.get("access_token"),
                    "refresh_token": token_content.get("refresh_token"),
                    "token_type": token_content.get("token_type", "Bearer"),
                    "expires_in": token_content.get("expires_in"),
                    "user_info": user_serializer.data,
                    "is_new_user": is_new_user,
                    "openid": login_result["openid"],
                    "login_type": "wechat_miniprogram",
                }

                logger.info(f"微信用户 {user.username} 登录成功 (openid: {login_result['openid']})")
                return AjaxResult.success(data=response_data, msg="微信登录成功")
            else:
                logger.error(f"微信用户 {user.username} OAuth2令牌获取失败: {token_content}")
                return AjaxResult.fail(msg="登录失败，请稍后重试")

        except Exception as e:
            logger.error(f"微信登录异常: {e}")
            return AjaxResult.fail(msg="登录服务暂时不可用，请稍后重试")


class WechatBindView(GenericAPIView):
    """
    微信账号绑定接口

    为已登录的系统用户绑定微信账号，实现微信登录功能。
    绑定后用户可以使用微信小程序直接登录系统，无需输入用户名密码。
    """

    permission_classes = [IsAuthenticated]
    serializer_class = WechatBindSerializer

    def post(self, request):
        """
        绑定微信账号到当前用户

        将微信小程序账号与当前登录的系统用户进行绑定。
        绑定成功后用户可以使用微信小程序进行快速登录。

        请求参数:
            请求体参数:
                - js_code (str, 必需): 微信小程序登录时获取的授权码

        请求数据示例:
            {
                "js_code": "0a1b2c3d4e5f6g7h8i9j"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "微信绑定成功",
                "data": {
                    "user_info": {用户详细信息},
                    "openid": "微信小程序用户唯一标识（只针对当前的小程序有效）",
                    "unionid": "微信开放平台唯一标识",
                    "bind_time": "绑定时间"
                }
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            bind_result = serializer.save()
            user = bind_result["user"]

            # 序列化用户信息
            user_serializer = UserOutputSerializer(user)

            response_data = {
                "user_info": user_serializer.data,
                "openid": bind_result["openid"],  # 微信小程序用户唯一标识（只针对当前的小程序有效）
                "unionid": bind_result.get("unionid"),
                "bind_time": bind_result["bind_time"].strftime("%Y-%m-%d %H:%M:%S"),
            }

            logger.info(f"用户 {user.username} 微信绑定成功")
            return AjaxResult.success(data=response_data, msg="微信绑定成功")

        except Exception as e:
            logger.error(f"微信绑定异常: {e}")
            return AjaxResult.fail(msg="绑定服务暂时不可用，请稍后重试")


class WechatUnbindView(GenericAPIView):
    """
    微信账号解绑接口

    解除当前用户与微信账号的绑定关系，解绑后用户无法使用微信登录。
    解绑操作不可逆，用户需要重新绑定才能恢复微信登录功能。
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        解绑当前用户的微信账号

        解除当前登录用户与微信账号的绑定关系。
        解绑后用户将无法使用微信小程序登录，需要使用用户名密码登录。

        请求参数:
            无需额外参数，基于当前登录用户进行解绑操作

        响应数据结构:
            {
                "code": 200,
                "msg": "微信解绑成功",
                "data": {
                    "user_info": {用户详细信息}
                }
            }
        """
        user = request.user

        if not user.is_wechat_bound():
            return AjaxResult.fail(msg="用户未绑定微信账号")

        try:
            with transaction.atomic():
                # 记录解绑前的信息
                openid = user.wechat_openid

                # 解绑微信账号
                user.unbind_wechat()

                # 序列化用户信息
                user_serializer = UserOutputSerializer(user)

                response_data = {
                    "user_info": user_serializer.data,
                }

                logger.info(f"用户 {user.username} 微信解绑成功 (原openid: {openid})")
                return AjaxResult.success(data=response_data, msg="微信解绑成功")

        except Exception as e:
            logger.error(f"微信解绑异常: {e}")
            return AjaxResult.fail(msg="解绑服务暂时不可用，请稍后重试")


class WechatStatusView(GenericAPIView):
    """
    微信绑定状态查询接口

    查询当前用户的微信账号绑定状态和相关信息。
    用于前端判断用户是否已绑定微信，以及显示微信账号相关信息。
    """

    permission_classes = [IsAuthenticated]
    serializer_class = WechatStatusSerializer

    def get(self, request):
        """
        查询当前用户微信绑定状态

        获取当前登录用户的微信账号绑定状态和相关信息。
        用于前端界面显示绑定状态和微信账号信息。

        请求参数:
            无需额外参数，基于当前登录用户查询绑定状态

        响应数据结构:
            {
                "code": 200,
                "msg": "查询成功",
                "data": {
                    "id": 用户ID,
                    "username": "用户名",
                    "is_wechat_bound": true/false,
                    "wechat_nickname": "微信昵称",
                    "wechat_avatar_url": "微信头像URL",
                    "wechat_bind_time": "绑定时间"
                }
            }
        """
        user = request.user
        serializer = self.get_serializer(user)

        return AjaxResult.success(data=serializer.data, msg="查询成功")


class WechatSessionRefreshView(GenericAPIView):
    """
    微信session_key刷新接口

    刷新当前用户的微信session_key，确保微信API调用的有效性。
    采用被动刷新策略，先检查有效性再决定是否刷新，提供完善的错误处理机制。
    """

    permission_classes = [IsAuthenticated]
    serializer_class = WechatSessionRefreshSerializer

    def post(self, request):
        """
        刷新当前用户的微信session_key

        检查并刷新当前用户的微信session_key，确保微信相关功能正常使用。
        采用智能刷新策略，仅在必要时进行刷新操作。

        请求参数:
            无需额外参数，基于当前登录用户进行session_key刷新

        响应数据结构:
            {
                "code": 200,
                "msg": "操作成功",
                "data": {
                    "status": "刷新状态(valid/refreshed)",
                    "message": "状态描述信息",
                    "session_key_valid": true/false
                }
            }
        """
        serializer = self.get_serializer(data={})
        serializer.is_valid(raise_exception=True)

        try:
            refresh_result = serializer.save()

            return AjaxResult.success(data=refresh_result, msg="操作成功")

        except Exception as e:
            logger.error(f"session_key刷新异常: {e}")
            return AjaxResult.fail(msg="刷新服务暂时不可用，请稍后重试")


class WechatConfigView(GenericAPIView):
    """
    微信小程序配置信息接口

    获取微信小程序的公开配置信息，用于前端判断微信登录功能是否可用。
    仅返回公开信息，不包含敏感的密钥等配置，确保安全性。
    """

    authentication_classes = []
    permission_classes = [AllowAny]

    def get(self, request):
        """
        获取微信小程序公开配置信息

        返回微信小程序的公开配置信息和服务状态，供前端判断是否启用微信登录功能。
        包含AppID和API服务可用性检查结果。

        请求参数:
            无需参数，公开接口

        响应数据结构:
            {
                "code": 200,
                "msg": "查询成功",
                "data": {
                    "app_id": "微信小程序AppID",
                    "api_available": true/false,
                    "login_enabled": true/false
                }
            }
        """
        try:
            # 检查微信API服务可用性
            wechat_api = get_wechat_api()
            api_available = True

            # 简单的健康检查（获取access_token）
            try:
                wechat_api._get_access_token()
            except Exception:
                api_available = False

            response_data = {
                "app_id": getattr(settings, "WECHAT_MINIPROGRAM_APP_ID", ""),
                "api_available": api_available,
                "login_enabled": bool(getattr(settings, "WECHAT_MINIPROGRAM_APP_ID", "")),
            }

            return AjaxResult.success(data=response_data, msg="查询成功")

        except Exception as e:
            logger.error(f"获取微信配置异常: {e}")
            return AjaxResult.fail(msg="配置服务暂时不可用")
