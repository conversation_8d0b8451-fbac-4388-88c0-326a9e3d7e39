#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : cache_manager.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""


from threading import Lock
from typing import Any, Optional, Set, Dict, TypeVar

from django.conf import settings
from django.core.cache import cache

from authsage.constant import RedisKey

T = TypeVar("T", bound="CacheManager")


def singleton_para(cls: T) -> T:
    """
    带参数的单例装饰器

    :param cls: 目标类
    :return: 装饰后的类
    """
    instances: Dict[Any, T] = {}
    lock = Lock()

    def wrapper(*args, **kwargs):
        key = (cls, args, frozenset(kwargs.items()))
        if key not in instances:
            with lock:
                # 双重检查锁定，防止多线程下重复创建实例
                if key not in instances:
                    instances[key] = cls(*args, **kwargs)
        return instances[key]

    return wrapper  # type: ignore


@singleton_para
class CacheManager:
    def __init__(self, prefix: Optional[RedisKey] = None):
        self.prefix = prefix.value if prefix else ""
        self._has_key: Set[str] = set()
        self._lock: Lock = Lock()

    def get(self, key: str) -> Optional[Any]:
        if not getattr(settings, "ENABLE_CACHE", False):
            return None

        cache_key = self._get_cache_key(key)
        return cache.get(cache_key)

    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> None:
        if not getattr(settings, "ENABLE_CACHE", False):
            return

        cache_key = self._get_cache_key(key)
        cache.set(cache_key, value, timeout or getattr(settings, "CACHE_TIMEOUT", 300))
        with self._lock:
            self._has_key.add(cache_key)

    def delete(self, key: str) -> None:
        cache_key = self._get_cache_key(key)
        cache.delete(cache_key)
        with self._lock:
            self._has_key.discard(cache_key)

    def clear(self) -> None:
        with self._lock:
            for key in self._has_key:
                cache.delete(key)
            self._has_key.clear()

    def _get_cache_key(self, key: str) -> str:
        return f"{self.prefix}:{key}" if self.prefix else key


def clear_all_cache() -> None:
    """
    清除所有缓存
    """
    cache.clear()


def show_all_cache() -> list:
    """
    显示所有缓存
    """
    if hasattr(cache, "keys"):
        # Some cache backends (e.g., Memcached) support listing keys
        return cache.keys("*")
    else:
        # Other backends may not support key listing
        return []


def clear_cache_by_prefix(prefix: str) -> None:
    """
    根据前缀清除缓存
    """
    if not prefix:
        return
    cache_keys = show_all_cache()
    for key in cache_keys:
        if key.startswith(prefix):
            cache.delete(key)


def clear_cache_by_manager(manager) -> None:
    """
    根据manager清除缓存
    """
    if not manager:
        return
    cache_manager = CacheManager(manager)
    cache_manager.clear()
