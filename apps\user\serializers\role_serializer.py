#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : role_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""
# 标准库导入

# 第三方库导入
from django.apps import apps
from django.db import transaction
from oauth2_provider.models import get_application_model
from rest_framework import serializers

# 本地应用导入
from apps.user.serializers.base import DynamicFieldsModelSerializer
from apps.user.serializers.permission_serializer import PermissionSerializer
from apps.user.tasks import check_application, check_dr_exist


class RoleCreateSerializer(DynamicFieldsModelSerializer):
    """角色信息序列化器"""

    role_name = serializers.CharField(required=True, max_length=128, label="first_name")
    total_name = serializers.SerializerMethodField()

    class Meta:
        fields = ("role_name", "permissions", "total_name")
        model = apps.get_model("auth", "Group")

    def get_total_name(self, obj):
        role_name = obj.get("role_name")
        name = "r_" + role_name
        return name

    def create(self, validated_data):
        role_name = validated_data.pop("role_name")

        name = "r_" + role_name

        validated_data["name"] = name

        # 角色检测
        if check_dr_exist(name):
            raise serializers.ValidationError("角色已存在")

        # application_id检测
        application_id = validated_data.get("application_id", None)
        if application_id and check_application(application_id) is None:
            raise serializers.ValidationError("应用不存在")

        with transaction.atomic():
            role = super().create(validated_data)

        return role


class RoleUpdateSerializer(DynamicFieldsModelSerializer):
    """角色信息序列化器"""

    role_name = serializers.CharField(
        required=False, max_length=128, label="first_name"
    )
    total_name = serializers.SerializerMethodField()

    class Meta:
        fields = ("role_name", "permissions", "total_name")
        model = apps.get_model("auth", "Group")

    def get_total_name(self, obj):
        role_name = obj.get("role_name")
        name = "r_" + role_name
        return name

    def update(self, instance, validated_data):
        role_name = validated_data.pop("role_name", None)

        if role_name:
            validated_data["name"] = "r_" + role_name

        # application_id检测
        application_id = validated_data.get("application_id", None)
        if application_id and check_application(application_id) is None:
            raise serializers.ValidationError("应用不存在")

        # 补全permissions
        permissions = validated_data.pop("permissions", [])

        for permission in instance.permissions.all():
            if permission.application_id != application_id:
                permissions.append(permission)

        validated_data["permissions"] = permissions

        with transaction.atomic():
            instance = super().update(instance, validated_data)

        return instance


class RoleOutputSerializer(DynamicFieldsModelSerializer):
    """角色信息序列化器"""

    class Meta:
        fields = ("id", "name", "user_all_permissions", "user_all_app")
        model = apps.get_model("auth", "Group")

    user_all_permissions = serializers.SerializerMethodField()

    user_all_app = serializers.SerializerMethodField()

    def get_user_all_permissions(self, obj):
        # 获取角色下所有权限
        permissions = obj.permissions.all()
        results = []
        for permission in permissions:
            results.append(PermissionSerializer(permission).data)

        return results

    def get_user_all_app(self, obj):
        # 获取角色下所有应用
        applications = set(obj.permissions.values_list("application_id", flat=True))
        results = []
        for application in applications:
            instance = get_application_model().objects.get(pk=application)
            results.append(instance.name)

        return results

    def to_representation(self, instance):
        # 从缓存中获取角色信息

        data = super().to_representation(instance)

        if hasattr(instance, "name"):
            data["total_name"] = instance.name
            data["name"] = instance.name[2:]

        return data
