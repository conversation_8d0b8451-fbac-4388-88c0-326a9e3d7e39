#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : urls.py
<AUTHOR> JT_DA
@Date     : 2025/07/02
@File_Desc:
"""

from django.urls import include, path
from rest_framework import routers

from apps.user.views.application_view_set import ApplicationViewSet
from apps.user.views.department_view_set import DepartmentViewSet
from apps.user.views.info_views import UserInfo
from apps.user.views.login_views import (
    CaptchaView,
    LoginView,
    LogoutView,
    RefreshView,
    SetPasswordView,
    ResetPasswordView,
)
from apps.user.views.wechat_login_views import (
    WechatLoginView,
    WechatBindView,
    WechatUnbindView,
    WechatStatusView,
    WechatSessionRefreshView,
    WechatConfigView,
)
from apps.user.views.wechat_face_auth_views import WechatFaceAuthUpdateView
from apps.user.views.permission_view_set import (
    HasPermission,
    PermissionStructure,
    PermissionViewSet,
)
from apps.user.views.role_view_set import RoleViewSet
from apps.user.views.user_view_set import UserViewSet

# 默认路由
router = routers.DefaultRouter()
router.register(r"application", ApplicationViewSet)
router.register(r"user", UserViewSet)
router.register(r"role", RoleViewSet)
router.register(r"department", DepartmentViewSet)
router.register(r"permission", PermissionViewSet)

urlpatterns = [
    path("", include(router.urls)),
    path("login/", LoginView.as_view(), name="login"),
    path("captcha/", CaptchaView.as_view(), name="captcha"),
    path("token/", RefreshView.as_view(), name="refresh"),
    path("logout/", LogoutView.as_view(), name="logout"),
    path("info/", UserInfo.as_view(), name="info"),
    path("has_perm/", HasPermission.as_view(), name="has_perm"),
    path("permission_structure/", PermissionStructure.as_view(), name="permission_structure"),
    path("set_password/", SetPasswordView.as_view(), name="set_password"),
    path("reset_password/", ResetPasswordView.as_view(), name="reset_password"),

    # 微信小程序登录相关路由
    path("wechat/login/", WechatLoginView.as_view(), name="wechat_login"),
    path("wechat/bind/", WechatBindView.as_view(), name="wechat_bind"),
    path("wechat/unbind/", WechatUnbindView.as_view(), name="wechat_unbind"),
    path("wechat/status/", WechatStatusView.as_view(), name="wechat_status"),
    path("wechat/refresh/", WechatSessionRefreshView.as_view(), name="wechat_refresh"),
    path("wechat/config/", WechatConfigView.as_view(), name="wechat_config"),

    # 微信人脸核身相关路由
    path("wechat/face_auth/", WechatFaceAuthUpdateView.as_view(), name="wechat_face_auth"),
]
