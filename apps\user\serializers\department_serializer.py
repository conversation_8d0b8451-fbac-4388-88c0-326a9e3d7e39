#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : department_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc: 
"""

from django.apps import apps
from django.db import transaction
from rest_framework import serializers

from apps.user.serializers.base import D<PERSON><PERSON>ieldsModelSerializer
from apps.user.tasks import check_application, check_department_before, check_dr_exist


class DepartmentCreateSerializer(DynamicFieldsModelSerializer):
    """部门信息序列化器"""

    first_name = serializers.CharField(
        required=True, max_length=128, label="first_name"
    )
    second_name = serializers.CharField(
        required=False, max_length=128, label="second_name"
    )
    third_name = serializers.CharField(
        required=False, max_length=128, label="third_name"
    )
    fourth_name = serializers.CharField(
        required=False, max_length=128, label="fourth_name"
    )
    total_name = serializers.SerializerMethodField()

    class Meta:
        fields = (
            "permissions",
            "first_name",
            "second_name",
            "third_name",
            "fourth_name",
            "total_name",
            # "application_id",
        )
        model = apps.get_model("auth", "Group")

    def get_total_name(self, obj):
        """
        获取部门多级名称综合名称

        :param obj: 序列化对象
        :return: 综合名称
        """
        first_name = obj.get("first_name")
        second_name = obj.get("second_name")
        third_name = obj.get("third_name")
        fourth_name = obj.get("fourth_name")

        name = "d_" + first_name
        if second_name:
            name += "-" + second_name
            if third_name:
                name += "-" + third_name
                if fourth_name:
                    name += "-" + fourth_name

        return name

    def create(self, validated_data):
        first_name = validated_data.pop("first_name")
        second_name = validated_data.pop("second_name", None)
        third_name = validated_data.pop("third_name", None)
        fourth_name = validated_data.pop("fourth_name", None)
        name = "d_" + first_name
        if second_name:
            name += "-" + second_name
            if third_name:
                name += "-" + third_name
                if fourth_name:
                    name += "-" + fourth_name

        permissions = validated_data.pop("permissions", None)

        validated_data["name"] = name
        # application_id = validated_data.get("application_id", None)

        # 部门检测
        if check_dr_exist(name):
            raise serializers.ValidationError("部门已存在")

        # 前置部门检测
        if check_department_before(name) is None and "-" in name:
            raise serializers.ValidationError("前置部门不存在，请先创建前置部门")

        # 检测application_id是否存在
        # if application_id and check_application(application_id) is None:
        #     raise serializers.ValidationError("应用不存在")

        with transaction.atomic():
            department = apps.get_model("auth", "Group").objects.create(
                **validated_data
            )
            if permissions:
                department.permissions.set(permissions)

        return department


class DepartmentUpdateSerializer(DynamicFieldsModelSerializer):
    """部门信息序列化器"""

    first_name = serializers.CharField(
        required=False, max_length=128, label="first_name"
    )
    second_name = serializers.CharField(
        required=False, max_length=128, label="second_name"
    )
    third_name = serializers.CharField(
        required=False, max_length=128, label="third_name"
    )
    fourth_name = serializers.CharField(
        required=False, max_length=128, label="fourth_name"
    )
    total_name = serializers.SerializerMethodField()

    class Meta:
        fields = (
            "permissions",
            "first_name",
            "second_name",
            "third_name",
            "fourth_name",
            "total_name",
            # "application_id",
        )
        model = apps.get_model("auth", "Group")

    def get_total_name(self, obj):
        first_name = obj.get("first_name")
        second_name = obj.get("second_name")
        third_name = obj.get("third_name")
        fourth_name = obj.get("fourth_name")

        name = "d_" + first_name
        if second_name:
            name += "-" + second_name
            if third_name:
                name += "-" + third_name
                if fourth_name:
                    name += "-" + fourth_name

        return name

    def update(self, instance, validated_data):
        first_name = validated_data.pop("first_name", None)
        second_name = validated_data.pop("second_name", None)
        third_name = validated_data.pop("third_name", None)
        fourth_name = validated_data.pop("fourth_name", None)

        if first_name:
            name = "d_" + first_name
            if second_name:
                name += "-" + second_name
                if third_name:
                    name += "-" + third_name
                    if fourth_name:
                        name += "-" + fourth_name
            validated_data["name"] = name

        # 检测application_id是否存在
        # application_id = validated_data.get("application_id", None)
        # if application_id and check_application(application_id) is None:
        #     raise serializers.ValidationError("应用不存在")

        with transaction.atomic():
            super().update(instance, validated_data)

        return instance


class DepartmentOutputSerializer(DynamicFieldsModelSerializer):
    """部门信息序列化器"""

    class Meta:
        fields = ("id", "permissions", "name")
        model = apps.get_model("auth", "Group")

    def to_representation(self, instance):

        data = super().to_representation(instance)

        if hasattr(instance, "name"):
            data["total_name"] = instance.name

            name_list = instance.name[2:].split("-")

            data["name"] = instance.name[2:]

            data["first_name"] = name_list[0]
            if len(name_list) > 1:
                data["second_name"] = name_list[1]
            if len(name_list) > 2:
                data["third_name"] = name_list[2]
            if len(name_list) > 3:
                data["fourth_name"] = name_list[3]

        return data
