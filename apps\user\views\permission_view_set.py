#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : permission_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""
# 标准库导入

# 第三方库导入
import django_filters
from django.apps import apps
from rest_framework import filters, mixins, serializers, viewsets
from rest_framework.generics import GenericAPIView

# 本地应用导入
from apps.user.filters import PermissionFilter
from apps.user.serializers.permission_serializer import PermissionSerializer
from utils import sort_data_by_id
from utils.ajax_result import AjaxResult
from utils.decorator import user_check
from utils.pagination import MyPageNumberPagination
from utils.user_check import is_admin


class PermissionViewSet(
    viewsets.GenericViewSet,
    mixins.ListModelMixin,
    mixins.RetrieveModelMixin,
    mixins.CreateModelMixin,
    mixins.UpdateModelMixin,
    mixins.DestroyModelMixin,
):
    """
    权限管理视图集

    提供系统权限的完整CRUD操作，包括权限的创建、查询、更新和删除功能。
    权限用于控制用户对系统资源的访问，支持按应用分组和层级结构管理。
    """

    queryset = apps.get_model("auth", "Permission").objects.all()

    serializer_class = PermissionSerializer

    pagination_class = MyPageNumberPagination

    filter_backends = [
        filters.OrderingFilter,
        django_filters.rest_framework.DjangoFilterBackend,
        filters.SearchFilter,
    ]

    ordering_fields = ["id", "name"]
    ordering = ["id"]

    search_fields = ["name", "codename"]

    filterset_class = PermissionFilter

    def list(self, request, *args, **kwargs):
        """
        获取权限列表

        查询系统中所有权限信息，支持分页查询、排序、搜索和过滤功能。
        返回权限的详细信息，用于权限管理和角色权限分配。

        请求参数:
            查询参数:
                - page (int, 可选): 页码，默认为1
                - page_size (int, 可选): 每页数量，默认为10
                - ordering (str, 可选): 排序字段，可选值: "id", "name", "-id", "-name"
                - search (str, 可选): 搜索关键词，支持权限名称和代码名搜索
                - application_id (int, 可选): 按应用ID过滤
                - permission_type (str, 可选): 按权限类型过滤

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "count": 总记录数,
                    "next": "下一页URL",
                    "previous": "上一页URL",
                    "results": [
                        {
                            "id": 权限ID,
                            "name": "权限名称",
                            "codename": "权限代码名",
                            "content_type": 内容类型ID,
                            "application_id": 应用ID,
                            "permission_type": "权限类型",
                            "parent_id": 父权限ID
                        }
                    ]
                }
            }
        """
        return super().list(request, *args, **kwargs)

    @user_check([is_admin])
    def create(self, request, *args, **kwargs):
        """
        创建新权限

        创建一个新的系统权限，用于控制用户对特定资源的访问。
        仅限管理员用户操作，创建前会检查权限代码名的唯一性。

        请求参数:
            请求体参数:
                - name (str, 必需): 权限名称
                - codename (str, 必需): 权限代码名，必须唯一
                - content_type (int, 必需): 内容类型ID
                - application_id (int, 可选): 关联的应用ID
                - permission_type (str, 可选): 权限类型
                - parent_id (int, 可选): 父权限ID，用于构建权限层级

        请求数据示例:
            {
                "name": "查看用户列表",
                "codename": "view_user_list",
                "content_type": 1,
                "application_id": 1,
                "permission_type": "menu",
                "parent_id": null
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": null
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        name = serializer.validated_data.get("name")
        codename = serializer.validated_data.get("codename")

        Permission = apps.get_model("auth", "Permission")

        if Permission.objects.filter(codename=codename).exists():
            return AjaxResult.fail(msg="权限已存在")

        self.perform_create(serializer)

        return AjaxResult.success()

    def retrieve(self, request, *args, **kwargs):
        """
        获取权限详细信息

        根据权限ID获取指定权限的详细信息，包括权限的基本属性和关联关系。
        用于权限信息查看和编辑前的数据获取。

        请求参数:
            路径参数:
                - id (int, 必需): 权限ID

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "id": 权限ID,
                    "name": "权限名称",
                    "codename": "权限代码名",
                    "content_type": 内容类型ID,
                    "application_id": 应用ID,
                    "permission_type": "权限类型",
                    "parent_id": 父权限ID
                }
            }
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return AjaxResult.success(data=serializer.data)

    @user_check([is_admin])
    def update(self, request, *args, **kwargs):
        """
        完全更新权限信息

        更新指定权限的完整信息，包括权限名称、代码名和关联关系。
        仅限管理员用户操作，支持完整更新和部分更新。

        请求参数:
            路径参数:
                - id (int, 必需): 权限ID
            请求体参数:
                - name (str, 必需): 权限名称
                - codename (str, 必需): 权限代码名
                - content_type (int, 必需): 内容类型ID
                - application_id (int, 可选): 关联的应用ID
                - permission_type (str, 可选): 权限类型
                - parent_id (int, 可选): 父权限ID

        请求数据示例:
            {
                "name": "编辑用户信息",
                "codename": "edit_user_info",
                "content_type": 1,
                "application_id": 1,
                "permission_type": "action",
                "parent_id": 2
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        self.perform_update(serializer)

        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return AjaxResult.success(msg="更新成功")

    @user_check([is_admin])
    def partial_update(self, request, *args, **kwargs):
        """
        部分更新权限信息

        部分更新指定权限的信息，只需提供需要修改的字段。
        仅限管理员用户操作，适用于单个或少数字段的修改场景。

        请求参数:
            路径参数:
                - id (int, 必需): 权限ID
            请求体参数:
                - name (str, 可选): 权限名称
                - codename (str, 可选): 权限代码名
                - content_type (int, 可选): 内容类型ID
                - application_id (int, 可选): 关联的应用ID
                - permission_type (str, 可选): 权限类型
                - parent_id (int, 可选): 父权限ID

        请求数据示例:
            {
                "name": "查看用户详情",
                "permission_type": "action"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        kwargs["partial"] = True
        self.update(request, *args, **kwargs)

        return AjaxResult.success(msg="更新成功")

    @user_check([is_admin])
    def destroy(self, request, *args, **kwargs):
        """
        删除权限

        删除指定的权限，删除后相关的角色和用户将失去该权限。
        仅限管理员用户操作，删除操作不可逆，请确保权限未被重要角色使用后再执行。

        请求参数:
            路径参数:
                - id (int, 必需): 要删除的权限ID

        响应数据结构:
            {
                "code": 200,
                "msg": "删除成功",
                "data": null
            }
        """
        instance = self.get_object()
        # 删除缓存
        self.perform_destroy(instance)

        return AjaxResult.success(msg="删除成功")


# 权限校验类
class HasPermission(GenericAPIView):
    """
    用户权限校验接口

    检查当前登录用户是否具有指定的权限，用于前端权限控制和功能访问验证。
    返回权限校验结果，帮助前端决定是否显示特定功能或菜单。
    """

    class HasPermissionSerializer(serializers.Serializer):
        permission = serializers.CharField(required=True, label="权限")

    serializer_class = HasPermissionSerializer

    def get(self, request):
        """
        检查用户权限

        验证当前登录用户是否具有指定的权限代码名对应的权限。
        用于前端动态权限控制，根据权限结果显示或隐藏功能模块。

        请求参数:
            查询参数:
                - permission (str, 必需): 权限代码名，如 "auth.view_user"

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "user": "用户名",
                    "permission": "权限代码名",
                    "has_permission": true/false
                }
            }
        """
        serializer = self.get_serializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        permission = serializer.validated_data.get("permission")

        user = request.user

        data = {
            "user": user.username,
            "permission": permission,
            "has_permission": user.has_perm(permission),
        }

        return AjaxResult.success(data=data)


# 权限结构类
class PermissionStructure(GenericAPIView):
    """
    权限结构树查询接口

    获取指定应用的权限树形结构，支持按角色过滤显示权限状态。
    用于权限管理界面的树形展示和角色权限分配功能。
    """

    class PermissionStructureSerializer(serializers.Serializer):
        application_id = serializers.IntegerField(required=True, label="应用id")
        role_id = serializers.IntegerField(required=False, label="角色id")

    serializer_class = PermissionStructureSerializer

    def get(self, request):
        """
        获取应用权限结构树

        根据应用ID获取该应用下的权限树形结构，如果提供角色ID则标记该角色拥有的权限。
        返回层级化的权限树，用于权限管理界面的展示和角色权限配置。

        请求参数:
            查询参数:
                - application_id (int, 必需): 应用ID
                - role_id (int, 可选): 角色ID，用于标记角色权限状态

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": [
                    {
                        "id": 权限ID,
                        "name": "权限名称",
                        "codename": "权限代码名",
                        "permission_type": "权限类型",
                        "content_type_id": 内容类型ID,
                        "has_perm": true/false,
                        "children": [子权限列表]
                    }
                ]
            }
        """
        serializer = self.get_serializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        application_id = serializer.validated_data["application_id"]
        role_id = serializer.validated_data["role_id"]

        Permission = apps.get_model("auth", "Permission")
        permissions = (
            Permission.objects.select_related("content_type")
            .filter(application_id=application_id)
            .values(
                "id",
                "name",
                "codename",
                "permission_type",
                "content_type_id",
                "parent_id",
            )
        )

        if not permissions:
            return AjaxResult.fail(msg="应用不存在权限")

        # 获取角色权限ID集合
        role_permission_ids = set(
            apps.get_model("auth", "Group")
            .objects.filter(id=role_id)
            .values_list("permissions__id", flat=True)
        )

        # 构建树结构
        data = self._build_tree(list(permissions), role_permission_ids)

        return AjaxResult.success(data=data)

    def _build_tree(self, permissions, role_permission_ids, parent_id=None):
        """构建权限树"""
        tree = []
        for perm in permissions:
            if perm["parent_id"] == parent_id:
                node = {
                    "id": perm["id"],
                    "name": perm["name"],
                    "codename": perm["codename"],
                    "permission_type": perm["permission_type"],
                    "content_type_id": perm["content_type_id"],
                    "has_perm": perm["id"] in role_permission_ids,
                }

                if children := self._build_tree(
                    permissions, role_permission_ids, perm["id"]
                ):
                    node["children"] = children

                tree.append(node)

        return sort_data_by_id(tree)
