#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : user_info_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/07/03
@File_Desc: 用户信息序列化器 - 优化版本
"""

import logging
from typing import List, Dict, Any, Optional

from django.apps import apps
from oauth2_provider.models import get_application_model

from apps.user.serializers.department_serializer import DepartmentOutputSerializer
from apps.user.serializers.role_serializer import RoleOutputSerializer
from apps.user.serializers.base import DynamicFieldsModelSerializer
from authsage.constant import RedisKey
from utils import sort_data_by_id
from utils.cache_manager import CacheManager
from apps.user.models import User

logger = logging.getLogger(__name__)


class UserInfoSerializer(DynamicFieldsModelSerializer):
    """
    用户信息序列化器
    
    功能：
    - 序列化用户基本信息
    - 处理用户角色和部门关系
    - 构建用户权限树
    - 缓存优化查询性能
    """
    
    class Meta:
        model = User
        fields = (
            # 用户标识相关字段
            "id",
            "username",
            "email",
            "wechat_openid",
            # 个人信息字段
            "real_name",
            "phone_number",
            "id_card_number",
            "wechat_nickname",
            "wechat_avatar_url",
            # 系统字段
            "detect_auth_result",
            "is_active",
            "is_staff",
            "is_superuser",
            "date_joined",
            "last_login",
            "groups",
            "user_permissions",
        )

    def to_representation(self, instance: User) -> Dict[str, Any]:
        """
        序列化用户信息为响应数据
        
        Args:
            instance: 用户实例
            
        Returns:
            Dict: 序列化后的用户数据
        """
        try:
            # 尝试从缓存获取完整用户信息
            cached_data = self._get_cached_user_info(instance.id)
            if cached_data:
                return cached_data

            # 获取基础用户数据
            data = super().to_representation(instance)
            
            # 处理用户组织关系（角色和部门）
            self._process_user_groups(data, instance)
            
            # 处理用户权限信息
            self._process_user_permissions(data, instance)
            
            # 缓存处理结果
            self._cache_user_info(instance.id, data)
            
            return data
            
        except Exception as e:
            logger.error(f"序列化用户信息失败 - 用户ID: {instance.id}, 错误: {str(e)}")
            # 返回基础数据，避免完全失败
            return super().to_representation(instance)

    def _get_cached_user_info(self, user_id: int) -> Optional[Dict[str, Any]]:
        """从缓存获取用户信息"""
        try:
            user_cache = CacheManager(RedisKey.USER_INFO)
            return user_cache.get(user_id)
        except Exception as e:
            logger.warning(f"获取用户缓存失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return None

    def _cache_user_info(self, user_id: int, data: Dict[str, Any]) -> None:
        """缓存用户信息"""
        try:
            user_cache = CacheManager(RedisKey.USER_INFO)
            user_cache.set(user_id, data)
        except Exception as e:
            logger.warning(f"缓存用户信息失败 - 用户ID: {user_id}, 错误: {str(e)}")

    def _process_user_groups(self, data: Dict[str, Any], instance: User) -> None:
        """
        处理用户的组织关系（角色和部门）
        
        Args:
            data: 用户数据字典
            instance: 用户实例
        """
        data["role"] = []
        data["department"] = []
        
        groups = data.pop("groups", [])
        if not groups:
            return
            
        try:
            # 批量获取组信息，减少数据库查询
            group_instances = self._get_groups_batch(groups)
            
            for group in group_instances:
                if group.name.startswith("r_"):
                    # 角色组
                    role_data = RoleOutputSerializer(
                        group, 
                        fields=["id", "name"]
                    ).data
                    data["role"].append(role_data)
                    
                elif group.name.startswith("d_"):
                    # 部门组  
                    dept_data = DepartmentOutputSerializer(
                        group,
                        fields=["id", "name"] 
                    ).data
                    data["department"].append(dept_data)
                    
        except Exception as e:
            logger.error(f"处理用户组织关系失败 - 用户ID: {instance.id}, 错误: {str(e)}")

    def _get_groups_batch(self, group_ids: List[int]) -> List:
        """
        批量获取组信息，优先从缓存获取
        
        Args:
            group_ids: 组ID列表
            
        Returns:
            List: 组实例列表
        """
        group_cache = CacheManager(RedisKey.GROUP)
        Group = apps.get_model("auth", "Group")
        groups = []
        uncached_ids = []
        
        # 先从缓存获取
        for group_id in group_ids:
            cached_group = group_cache.get(group_id)
            if cached_group:
                groups.append(cached_group)
            else:
                uncached_ids.append(group_id)
        
        # 批量查询未缓存的组
        if uncached_ids:
            try:
                uncached_groups = Group.objects.filter(id__in=uncached_ids)
                for group in uncached_groups:
                    group_cache.set(group.id, group)
                    groups.append(group)
            except Exception as e:
                logger.error(f"批量查询组信息失败 - 组IDs: {uncached_ids}, 错误: {str(e)}")
                
        return groups

    def _process_user_permissions(self, data: Dict[str, Any], instance: User) -> None:
        """
        处理用户权限信息
        
        Args:
            data: 用户数据字典  
            instance: 用户实例
        """
        try:
            # 获取当前请求用户（用于权限检查）
            current_user = self.context.get("request", {}).user if hasattr(self.context.get("request", {}), 'user') else instance
            
            # 获取用户所有权限ID
            permission_ids = self._get_user_permission_ids(current_user)
            
            if not permission_ids:
                data["user_all_app"] = []
                data["user_all_permissions"] = {}
                return
            
            # 获取权限实例
            permissions = self._get_permissions_batch(permission_ids)
            
            # 按应用分组处理权限
            self._group_permissions_by_app(data, permissions)
            
        except Exception as e:
            logger.error(f"处理用户权限失败 - 用户ID: {instance.id}, 错误: {str(e)}")
            data["user_all_app"] = []
            data["user_all_permissions"] = {}

    def _get_user_permission_ids(self, user: User) -> List[int]:
        """
        获取用户的所有权限ID（直接权限 + 组权限）
        
        Args:
            user: 用户实例
            
        Returns:
            List[int]: 权限ID列表
        """
        try:
            # 用户直接权限
            user_permission_ids = list(user.user_permissions.values_list("id", flat=True))
            
            # 用户组权限
            group_permission_ids = list(user.groups.values_list("permissions__id", flat=True))
            
            # 合并去重，移除None值
            all_permission_ids = list(set(user_permission_ids + group_permission_ids))
            return [pid for pid in all_permission_ids if pid is not None]
            
        except Exception as e:
            logger.error(f"获取用户权限ID失败 - 用户ID: {user.id}, 错误: {str(e)}")
            return []

    def _get_permissions_batch(self, permission_ids: List[int]) -> List:
        """
        批量获取权限实例
        
        Args:
            permission_ids: 权限ID列表
            
        Returns:
            List: 权限实例列表
        """
        permission_cache = CacheManager(RedisKey.PERMISSION)
        Permission = apps.get_model("auth", "Permission")
        permissions = []
        uncached_ids = []
        
        # 先从缓存获取
        for perm_id in permission_ids:
            cached_perm = permission_cache.get(perm_id)
            if cached_perm:
                permissions.append(cached_perm)
            else:
                uncached_ids.append(perm_id)
        
        # 批量查询未缓存的权限
        if uncached_ids:
            try:
                uncached_permissions = Permission.objects.filter(id__in=uncached_ids)
                for perm in uncached_permissions:
                    permission_cache.set(perm.id, perm)
                    permissions.append(perm)
            except Exception as e:
                logger.error(f"批量查询权限失败 - 权限IDs: {uncached_ids}, 错误: {str(e)}")
                
        return permissions

    def _group_permissions_by_app(self, data: Dict[str, Any], permissions: List) -> None:
        """
        按应用分组权限并构建权限树
        
        Args:
            data: 用户数据字典
            permissions: 权限实例列表
        """
        # 按应用ID分组权限
        app_permissions = {}
        for perm in permissions:
            app_id = perm.application_id
            if app_id not in app_permissions:
                app_permissions[app_id] = []
            app_permissions[app_id].append(perm.id)
        
        # 获取应用信息并构建权限树
        data["user_all_app"] = []
        data["user_all_permissions"] = {}
        
        applications = self._get_applications_batch(list(app_permissions.keys()))
        
        for app in applications:
            data["user_all_app"].append(app.name)
            permission_tree = self._build_permission_tree(
                app.id, 
                app_permissions.get(app.id, [])
            )
            data["user_all_permissions"][app.name] = permission_tree

    def _get_applications_batch(self, app_ids: List[int]) -> List:
        """
        批量获取应用实例
        
        Args:
            app_ids: 应用ID列表
            
        Returns:
            List: 应用实例列表
        """
        application_cache = CacheManager(RedisKey.APPLICATION)
        Application = get_application_model()
        applications = []
        uncached_ids = []
        
        # 先从缓存获取
        for app_id in app_ids:
            cached_app = application_cache.get(app_id)
            if cached_app:
                applications.append(cached_app)
            else:
                uncached_ids.append(app_id)
        
        # 批量查询未缓存的应用
        if uncached_ids:
            try:
                uncached_apps = Application.objects.filter(id__in=uncached_ids)
                for app in uncached_apps:
                    application_cache.set(app.id, app)
                    applications.append(app)
            except Exception as e:
                logger.error(f"批量查询应用失败 - 应用IDs: {uncached_ids}, 错误: {str(e)}")
                
        return applications

    def _build_permission_tree(self, application_id: int, user_permission_ids: List[int]) -> Optional[List[Dict]]:
        """
        构建权限树结构
        
        Args:
            application_id: 应用ID
            user_permission_ids: 用户权限ID列表
            
        Returns:
            Optional[List[Dict]]: 权限树结构或None
        """
        try:
            Permission = apps.get_model("auth", "Permission")
            permissions = Permission.objects.filter(application_id=application_id)
            
            if not permissions.exists():
                return None
                
            # 构建树形结构
            tree = self._build_tree_recursive(
                list(permissions), 
                user_permission_ids, 
                parent_id=None
            )
            
            return sort_data_by_id(tree)
            
        except Exception as e:
            logger.error(f"构建权限树失败 - 应用ID: {application_id}, 错误: {str(e)}")
            return None

    def _build_tree_recursive(
        self, 
        permissions: List, 
        user_permission_ids: List[int], 
        parent_id: Optional[int] = None
    ) -> List[Dict]:
        """
        递归构建权限树
        
        Args:
            permissions: 权限列表
            user_permission_ids: 用户权限ID列表
            parent_id: 父级权限ID
            
        Returns:
            List[Dict]: 权限树节点列表
        """
        tree = []
        
        for perm in permissions:
            if perm.parent_id == parent_id:
                node = {
                    "id": perm.id,
                    "name": perm.name,
                    "codename": perm.codename,
                    "permission_type": perm.permission_type,
                    "content_type_id": perm.content_type.id if perm.content_type else None,
                    "has_perm": perm.id in user_permission_ids,
                }
                
                # 递归获取子权限
                children = self._build_tree_recursive(
                    permissions, 
                    user_permission_ids, 
                    perm.id
                )
                
                if children:
                    node["children"] = children
                    
                tree.append(node)
        
        return tree
