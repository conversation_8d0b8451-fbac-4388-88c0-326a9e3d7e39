#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : application_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc:
"""

# 标准库导入
from django.apps import apps

# 第三方库导入
from rest_framework import mixins, viewsets

# 本地应用导入
from apps.user.filters import ApplicationFilter
from apps.user.serializers.application_serializer import ApplicationSerializer
from utils.ajax_result import AjaxResult
from utils.decorator import user_check
from utils.pagination import MyPageNumberPagination
from utils.user_check import is_admin


class ApplicationViewSet(
    viewsets.GenericViewSet,
    mixins.CreateModelMixin,
    mixins.DestroyModelMixin,
    mixins.ListModelMixin,
):
    """
    OAuth2应用管理视图集

    提供OAuth2应用的创建、查询、删除等管理功能，用于系统中各个应用的认证授权配置管理。
    支持应用列表查询、新应用创建和应用删除操作。
    """

    queryset = apps.get_model("oauth2_provider", "Application").objects.all()
    serializer_class = ApplicationSerializer
    filterset_class = ApplicationFilter
    pagination_class = MyPageNumberPagination

    def list(self, request, *args, **kwargs):
        """
        获取OAuth2应用列表

        查询系统中所有已注册的OAuth2应用信息，支持分页查询和过滤功能。
        主要用于管理员查看和管理系统中的各个应用配置。

        请求参数:
            查询参数:
                - page (int, 可选): 页码，默认为1
                - page_size (int, 可选): 每页数量，默认为10
                - name (str, 可选): 应用名称过滤
                - client_type (str, 可选): 客户端类型过滤

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "count": 总记录数,
                    "next": "下一页URL",
                    "previous": "上一页URL",
                    "results": [
                        {
                            "id": 应用ID,
                            "name": "应用名称",
                            "client_id": "客户端ID",
                            "client_type": "客户端类型",
                            "authorization_grant_type": "授权类型",
                            "redirect_uris": "重定向URI"
                        }
                    ]
                }
            }
        """
        return super().list(request, *args, **kwargs)

    @user_check([is_admin])
    def create(self, request, *args, **kwargs):
        """
        创建新的OAuth2应用

        创建一个新的OAuth2应用配置，用于系统中新应用的认证授权。
        仅限管理员用户操作，创建成功后会自动生成客户端ID和密钥。

        请求参数:
            请求体参数:
                - name (str, 必需): 应用名称
                - client_type (str, 必需): 客户端类型，可选值: "confidential", "public"
                - authorization_grant_type (str, 必需): 授权类型，可选值: "authorization-code", "client-credentials"
                - redirect_uris (str, 可选): 重定向URI，多个URI用空格分隔

        请求数据示例:
            {
                "name": "示例应用",
                "client_type": "confidential",
                "authorization_grant_type": "authorization-code",
                "redirect_uris": "https://example.com/callback"
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "创建成功",
                "data": null
            }
        """
        # 获取参数，验证参数
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # 创建应用
        self.perform_create(serializer)
        # 结果
        return AjaxResult.success(msg="创建成功")

    @user_check([is_admin])
    def destroy(self, request, *args, **kwargs):
        """
        删除OAuth2应用

        删除指定的OAuth2应用配置，删除后该应用将无法进行认证授权。
        仅限管理员用户操作，删除操作不可逆，请谨慎使用。

        请求参数:
            路径参数:
                - id (int, 必需): 要删除的应用ID

        响应数据结构:
            {
                "code": 200,
                "msg": "删除成功",
                "data": null
            }
        """
        instance = self.get_object()
        self.perform_destroy(instance)

        return AjaxResult.success(msg="删除成功")
