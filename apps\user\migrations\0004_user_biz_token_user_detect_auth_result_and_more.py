# Generated by Django 4.1.13 on 2025-07-24 14:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0003_wechatminiprogramconfig_user_wechat_avatar_url_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='biz_token',
            field=models.CharField(blank=True, help_text='人脸核身流程的唯一业务标识，用于追踪核身流程', max_length=255, null=True, verbose_name='人脸核身业务标识'),
        ),
        migrations.AddField(
            model_name='user',
            name='detect_auth_result',
            field=models.BooleanField(blank=True, default=False, help_text='人脸核身结果：True表示核身成功，False表示核身失败', null=True, verbose_name='人脸核身结果'),
        ),
        migrations.AddField(
            model_name='user',
            name='detect_auth_time',
            field=models.DateTimeField(blank=True, help_text='人脸核身完成的日期时间', null=True, verbose_name='人脸核身时间'),
        ),
        migrations.AddField(
            model_name='user',
            name='real_name',
            field=models.CharField(blank=True, help_text='用户的真实姓名，通过人脸核身验证获得', max_length=100, null=True, verbose_name='真实姓名'),
        ),
    ]
