#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : reset_password_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/01/14
@File_Desc: 重置密码序列化器
"""

from rest_framework import serializers
from django.contrib.auth import get_user_model

User = get_user_model()


class ResetPasswordSerializer(serializers.Serializer):
    """重置密码序列化器"""

    username = serializers.CharField(required=True, help_text="用户名")

    def validate_username(self, value):
        """验证用户名是否存在"""
        try:
            User.objects.get(username=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("用户名不存在")
        return value
