#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : info_views.py
<AUTHOR> JT_DA
@Date     : 2025/07/02
@File_Desc:
"""

from rest_framework.generics import GenericAPIView

from apps.user.serializers.user_info_serializer import UserInfoSerializer
from utils.ajax_result import AjaxResult


class UserInfo(GenericAPIView):
    """
    用户信息查询接口

    获取当前登录用户的详细信息，包括基本信息、角色权限、部门归属等。
    采用UserInfoSerializer进行数据序列化，支持Redis缓存优化和批量查询，
    显著提升查询性能。返回完整的权限树结构，便于前端权限控制。
    """

    serializer_class = UserInfoSerializer

    def get(self, request):
        """
        获取当前用户详细信息

        通过UserInfoSerializer获取当前登录用户的完整信息，包括基本资料、
        微信信息、角色部门关系、权限树等。采用多级缓存策略和批量查询优化，
        返回的权限信息按应用分组并构建树形结构，支持权限状态标识。

        请求参数:
            无需额外参数，基于当前登录用户的身份获取信息

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "id": 用户ID,
                    "username": "用户名",
                    "email": "邮箱地址",
                    "wechat_openid": "微信OpenID",
                    "phone_number": "手机号码",
                    "id_card_number": "身份证号",
                    "wechat_nickname": "微信昵称",
                    "wechat_avatar_url": "微信头像URL",
                    "detect_auth_result": "检测认证结果",
                    "is_active": 是否激活,
                    "is_staff": 是否员工,
                    "is_superuser": 是否超级管理员,
                    "date_joined": "注册时间",
                    "last_login": "最后登录时间",
                    "user_permissions": [用户直接权限ID列表],
                    "role": [
                        {
                            "id": 角色ID,
                            "name": "角色名称"
                        }
                    ],
                    "department": [
                        {
                            "id": 部门ID,
                            "name": "部门名称"
                        }
                    ],
                    "user_all_app": ["应用名称列表"],
                    "user_all_permissions": {
                        "应用名称": [
                            {
                                "id": 权限ID,
                                "name": "权限名称",
                                "codename": "权限代码",
                                "permission_type": "权限类型",
                                "content_type_id": 内容类型ID,
                                "has_perm": 是否拥有该权限,
                                "children": [子权限列表]
                            }
                        ]
                    }
                }
            }
        """
        user = request.user
        serializer = self.get_serializer(user)

        return AjaxResult.success(data=serializer.data)
