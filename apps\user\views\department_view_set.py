#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : department_view_set.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc: 
"""

# 标准库导入

# 第三方库导入
import django_filters
from django.apps import apps
from rest_framework import filters, viewsets
from rest_framework.permissions import IsAuthenticated

# 本地应用导入
from apps.user.filters import DepartmentFilter
from apps.user.serializers.department_serializer import (
    DepartmentCreateSerializer,
    DepartmentOutputSerializer,
    DepartmentUpdateSerializer,
)
from utils.ajax_result import AjaxResult
from utils.decorator import user_check
from utils.ip_whitelist_permission import IPWhitelistPermission
from utils.pagination import MyPageNumberPagination
from utils.user_check import is_admin


class DepartmentViewSet(viewsets.ModelViewSet):
    """
    部门管理视图集

    提供部门信息的完整CRUD操作，包括部门的创建、查询、更新和删除功能。
    部门信息用于组织架构管理和用户权限分组，支持多级部门结构。
    """

    queryset = (
        apps.get_model("auth", "Group")
        .objects.filter(name__startswith="d_")
        .prefetch_related("permissions")
    )
    filterset_class = DepartmentFilter

    permission_classes = [IsAuthenticated | IPWhitelistPermission]

    pagination_class = MyPageNumberPagination
    filter_backends = [
        filters.OrderingFilter,
        django_filters.rest_framework.DjangoFilterBackend,
        filters.SearchFilter,
    ]

    ordering_fields = ["id", "name"]
    ordering = ["id"]
    search_fields = ["name"]

    def get_serializer_class(self):
        if self.action in ["create"]:
            return DepartmentCreateSerializer
        elif self.action in ["list", "retrieve"]:
            return DepartmentOutputSerializer
        elif self.action in ["update", "partial_update"]:
            return DepartmentUpdateSerializer
        return DepartmentOutputSerializer

    def list(self, request, *args, **kwargs):
        """
        获取部门列表

        查询系统中所有部门信息，支持分页查询、排序和搜索功能。
        返回部门的基本信息和权限配置，用于部门管理和用户分配。

        请求参数:
            查询参数:
                - page (int, 可选): 页码，默认为1
                - page_size (int, 可选): 每页数量，默认为10
                - ordering (str, 可选): 排序字段，可选值: "id", "name", "-id", "-name"
                - search (str, 可选): 搜索关键词，支持部门名称搜索

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "count": 总记录数,
                    "next": "下一页URL",
                    "previous": "上一页URL",
                    "results": [
                        {
                            "id": 部门ID,
                            "name": "部门名称",
                            "total_name": "完整部门名称",
                            "first_name": "一级部门名称",
                            "second_name": "二级部门名称",
                            "third_name": "三级部门名称",
                            "fourth_name": "四级部门名称",
                            "permissions": [权限ID列表]
                        }
                    ]
                }
            }
        """
        return super().list(request, *args, **kwargs)

    @user_check([is_admin])
    def create(self, request, *args, **kwargs):
        """
        创建新部门

        创建一个新的部门，支持多级部门结构配置。
        仅限管理员用户操作，创建成功后可为部门分配权限和用户。

        请求参数:
            请求体参数:
                - first_name (str, 必需): 一级部门名称
                - second_name (str, 可选): 二级部门名称
                - third_name (str, 可选): 三级部门名称
                - fourth_name (str, 可选): 四级部门名称
                - permissions (list, 可选): 部门权限ID列表

        请求数据示例:
            {
                "first_name": "技术部",
                "second_name": "研发中心",
                "third_name": "后端组",
                "permissions": [1, 2, 3]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": null
            }
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        self.perform_create(serializer)

        return AjaxResult.success()

    def retrieve(self, request, *args, **kwargs):
        """
        获取部门详细信息

        根据部门ID获取指定部门的详细信息，包括部门层级结构和权限配置。
        用于部门信息查看和编辑前的数据获取。

        请求参数:
            路径参数:
                - id (int, 必需): 部门ID

        响应数据结构:
            {
                "code": 200,
                "msg": "success",
                "data": {
                    "id": 部门ID,
                    "name": "部门名称",
                    "total_name": "完整部门名称",
                    "first_name": "一级部门名称",
                    "second_name": "二级部门名称",
                    "third_name": "三级部门名称",
                    "fourth_name": "四级部门名称",
                    "permissions": [权限ID列表]
                }
            }
        """
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return AjaxResult.success(data=serializer.data)

    @user_check([is_admin])
    def update(self, request, *args, **kwargs):
        """
        更新部门信息

        更新指定部门的完整信息，包括部门名称层级和权限配置。
        仅限管理员用户操作，支持完整更新和部分更新。

        请求参数:
            路径参数:
                - id (int, 必需): 部门ID
            请求体参数:
                - first_name (str, 必需): 一级部门名称
                - second_name (str, 可选): 二级部门名称
                - third_name (str, 可选): 三级部门名称
                - fourth_name (str, 可选): 四级部门名称
                - permissions (list, 可选): 部门权限ID列表

        请求数据示例:
            {
                "first_name": "技术部",
                "second_name": "研发中心",
                "third_name": "前端组",
                "permissions": [1, 2, 4, 5]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        self.perform_update(serializer)
        if getattr(instance, "_prefetched_objects_cache", None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return AjaxResult.success(msg="更新成功")

    @user_check([is_admin])
    def partial_update(self, request, *args, **kwargs):
        """
        部分更新部门信息

        部分更新指定部门的信息，只需提供需要修改的字段。
        仅限管理员用户操作，适用于单个或少数字段的修改场景。

        请求参数:
            路径参数:
                - id (int, 必需): 部门ID
            请求体参数:
                - first_name (str, 可选): 一级部门名称
                - second_name (str, 可选): 二级部门名称
                - third_name (str, 可选): 三级部门名称
                - fourth_name (str, 可选): 四级部门名称
                - permissions (list, 可选): 部门权限ID列表

        请求数据示例:
            {
                "second_name": "产品中心",
                "permissions": [1, 3, 5]
            }

        响应数据结构:
            {
                "code": 200,
                "msg": "更新成功",
                "data": null
            }
        """
        kwargs["partial"] = True
        return self.update(request, *args, **kwargs)

    @user_check([is_admin])
    def destroy(self, request, *args, **kwargs):
        """
        删除部门

        删除指定的部门信息，删除后该部门下的用户将失去部门归属。
        仅限管理员用户操作，删除操作不可逆，请确保部门下无关联用户后再执行。

        请求参数:
            路径参数:
                - id (int, 必需): 要删除的部门ID

        响应数据结构:
            {
                "code": 200,
                "msg": "删除成功",
                "data": null
            }
        """
        instance = self.get_object()
        self.perform_destroy(instance)

        return AjaxResult.success(msg="删除成功")
