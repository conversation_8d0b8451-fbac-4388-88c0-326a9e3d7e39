from django.utils import timezone
from rest_framework import serializers

from apps.operation_log.models import OperationLog, LoginLog


class OperationLogSerializer(serializers.ModelSerializer):
    created_time = serializers.DateTimeField(format="%Y-%m-%d %H:%M:%S", read_only=True)

    class Meta:
        model = OperationLog
        fields = "__all__"


class LoginLogSerializer(serializers.ModelSerializer):
    login_time_format = serializers.DateTimeField(
        source="login_time", format="%Y-%m-%d %H:%M:%S"
    )
    logout_time_format = serializers.DateTimeField(
        source="logout_time", format="%Y-%m-%d %H:%M:%S"
    )

    class Meta:
        model = LoginLog
        fields = "__all__"
