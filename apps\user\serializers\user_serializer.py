#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : user_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""
# 标准库导入
import re

# 第三方库导入
from django.db import transaction
from oauth2_provider.models import get_application_model
from rest_framework import serializers

# 本地应用导入
from apps.user.serializers.base import DynamicFieldsModelSerializer
from apps.user.serializers.role_serializer import RoleOutputSerializer
from apps.user.models import User


class UserCreateSerializer(DynamicFieldsModelSerializer):
    """用户创建序列化器"""

    class Meta:
        model = User
        fields = (
            "username",
            "phone_number",
            "id_card_number",
            "password",
            "groups",
            "user_permissions",
        )

    def validate_phone_number(self, value):
        """验证手机号格式和唯一性"""
        if value is None or value == "":
            return value

        # 手机号格式验证：11位数字，以1开头，第二位为3-9
        phone_pattern = r"^1[3-9]\d{9}$"
        if not re.match(phone_pattern, value):
            raise serializers.ValidationError(
                "手机号格式不正确，请输入有效的11位手机号"
            )

        # 手机号唯一性验证
        if User.objects.filter(phone_number=value).exists():
            raise serializers.ValidationError("该手机号已被注册")

        return value

    def validate_username(self, value):
        """验证用户名"""
        if not value or not value.strip():
            raise serializers.ValidationError("用户名不能为空")

        # 用户名长度验证
        if len(value.strip()) < 2:
            raise serializers.ValidationError("用户名长度不能少于2个字符")

        if len(value.strip()) > 150:
            raise serializers.ValidationError("用户名长度不能超过150个字符")

        # 用户名唯一性验证
        if User.objects.filter(username=value).exists():
            raise serializers.ValidationError("用户名已存在")

        return value.strip()

    def validate(self, attrs):
        """整体数据验证"""
        # 密码验证
        password = attrs.get("password")
        if not password:
            raise serializers.ValidationError({"password": "密码不能为空"})

        if len(password) < 6:
            raise serializers.ValidationError({"password": "密码长度不能少于6个字符"})

        return attrs

    def create(self, validated_data):
        """创建用户"""
        # 提取需要单独处理的字段
        password = validated_data.pop("password", None)
        groups = validated_data.pop("groups", None)
        user_permissions = validated_data.pop("user_permissions", None)

        # 在事务中创建用户，确保数据一致性
        with transaction.atomic():
            # 创建用户基础信息
            user = super().create(validated_data)

            # 设置用户密码（加密存储）
            if password:
                user.set_password(password)

            # 设置用户组（角色/部门）
            if groups:
                user.groups.set(groups)

            # 设置用户权限
            if user_permissions:
                user.user_permissions.set(user_permissions)

            # 保存用户信息
            user.save()

        return user


class UserOutputSerializer(DynamicFieldsModelSerializer):
    """
    用户信息序列化器

    用于序列化用户的完整信息，包括基本资料、人脸核身信息、微信小程序绑定信息、
    角色权限等。支持人脸核身功能和微信小程序用户管理。
    """

    class Meta:
        model = User
        fields = (
            # 基本信息字段
            "id",
            "username",
            "email",
            "phone_number",
            "id_card_number",
            "is_active",
            "is_staff",
            "is_superuser",
            # 人脸核身相关字段
            "real_name",
            "biz_token",
            "detect_auth_time",
            "detect_auth_result",
            # 微信小程序相关字段
            "wechat_openid",
            "wechat_unionid",
            "wechat_session_key",
            "wechat_nickname",
            "wechat_avatar_url",
            "wechat_bind_time",
            # 权限相关字段
            "groups",
            "user_permissions",
            "role",
            "department",
            "user_all_app",
        )

    role = serializers.SerializerMethodField()
    department = serializers.SerializerMethodField()
    user_all_app = serializers.SerializerMethodField()

    def get_role(self, instance):
        groups = instance.groups.all()

        results = []
        for role in groups:
            if not role.name.startswith("r_"):
                continue
            results.append(RoleOutputSerializer(role, fields=["id", "name"]).data)

        return results

    def get_department(self, instance):
        groups = instance.groups.all()

        results = []
        for dept in groups:
            if not dept.name.startswith("d_"):
                continue
            results.append(RoleOutputSerializer(dept, fields=["id", "name"]).data)

        return results

    def get_user_all_app(self, instance):
        # user_permission_ids = instance.user_permissions.values_list('id', flat=True)
        # group_permission_ids = instance.groups.values_list('permissions__id', flat=True)
        # all_permission_ids = list(set(user_permission_ids) | set(group_permission_ids))
        # if None in all_permission_ids:
        #     all_permission_ids.remove(None)

        results = []

        # 获取用户直接拥有的权限
        user_permissions = instance.user_permissions.all()

        # 获取用户所属的用户组
        user_groups = instance.groups.all()

        # 获取用户组的权限
        group_permissions = []
        for group in user_groups:
            group_permissions.extend(group.permissions.all())

        # 合并用户直接拥有的权限和用户组的权限
        all_permissions = list(user_permissions) + list(group_permissions)

        # 去重，确保权限唯一
        unique_permissions = set(all_permissions)

        user_all_app = set(
            [permission.application_id for permission in unique_permissions]
        )

        for app in user_all_app:
            app_instance = get_application_model().objects.get(pk=app)
            results.append(app_instance.name)

        return results

    def to_representation(self, instance):
        # 正常获取用户信息
        data = super().to_representation(instance)

        return data


class UserUpdateSerializer(DynamicFieldsModelSerializer):
    """用户信息序列化器"""

    password = serializers.CharField(required=False, write_only=True)

    class Meta:
        model = User
        fields = (
            "username",
            "phone_number",
            "id_card_number",
            "password",
            "groups",
            "user_permissions",
        )

    def update(self, instance, validated_data):
        password = validated_data.pop("password", None)

        with transaction.atomic():
            user = super().update(instance, validated_data)
            if password:
                user.set_password(password)
            user.save()

        return user