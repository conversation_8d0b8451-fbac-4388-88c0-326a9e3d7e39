#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : ip_whitelist_permission.py
<AUTHOR> JT_DA
@Date     : 2025/06/27
@File_Desc: 
"""

from django.conf import settings
from ipware import get_client_ip
from rest_framework import permissions


class IPWhitelistPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        client_ip, _ = get_client_ip(request)
        # 若ip为172开头，则为内网ip，直接放行
        if client_ip.startswith("172"):
            return True
        return client_ip in settings.IP_WHITE_LIST
