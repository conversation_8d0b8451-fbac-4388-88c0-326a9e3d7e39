#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : permission_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""

from django.apps import apps

from apps.user.serializers.base import DynamicFieldsModelSerializer


class PermissionSerializer(DynamicFieldsModelSerializer):
    """权限信息序列化器"""

    # content_type = serializers.SerializerMethodField()

    class Meta:
        fields = "__all__"
        model = apps.get_model("auth", "Permission")

    # def get_content_type(self, obj):
    #     return ContentTypeSerializer(obj.content_type).data


class ContentTypeSerializer(DynamicFieldsModelSerializer):
    """权限信息序列化器"""

    class Meta:
        fields = "__all__"
        model = apps.get_model("contenttypes", "ContentType")
