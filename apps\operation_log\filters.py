import django_filters

from apps.operation_log.models import OperationLog, LoginLog


class OperationLogFilter(django_filters.FilterSet):
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")
    username = django_filters.Char<PERSON>ilter(field_name="username", lookup_expr="icontains")
    account = django_filters.CharFilter(field_name="account", lookup_expr="icontains")
    button = django_filters.CharFilter(field_name="button", lookup_expr="icontains")
    kind = django_filters.CharFilter(field_name="kind", lookup_expr="icontains")
    url = django_filters.CharFilter(field_name="url", lookup_expr="icontains")
    belong = django_filters.CharFilter(field_name="belong", lookup_expr="icontains")
    ip = django_filters.Char<PERSON>ilter(field_name="ip", lookup_expr="icontains")
    created = django_filters.DateTimeFromToRangeFilter(field_name="created")
    logout_time = django_filters.DateTimeFromToRangeFilter(field_name="logout_time")

    class Meta:
        model = OperationLog
        fields = [
            "id",
            "username",
            "account",
            "button",
            "kind",
            "url",
            "belong",
            "ip",
            "created",
            "logout_time",
        ]


class LoginLogFilter(django_filters.FilterSet):
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")
    username = django_filters.CharFilter(field_name="username", lookup_expr="icontains")
    department = django_filters.CharFilter(
        field_name="department", lookup_expr="icontains"
    )
    account = django_filters.CharFilter(field_name="account", lookup_expr="icontains")
    ip = django_filters.CharFilter(field_name="ip", lookup_expr="icontains")
    login_time = django_filters.DateTimeFromToRangeFilter(field_name="login_time")
    logout_time = django_filters.DateTimeFromToRangeFilter(field_name="logout_time")

    class Meta:
        model = LoginLog
        fields = [
            "id",
            "username",
            "department",
            "account",
            "ip",
            "login_time",
            "logout_time",
        ]
