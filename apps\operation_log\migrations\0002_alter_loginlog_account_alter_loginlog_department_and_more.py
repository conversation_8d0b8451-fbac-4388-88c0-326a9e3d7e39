# Generated by Django 4.1.13 on 2025-07-02 17:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("operation_log", "0001_initial"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="loginlog",
            name="account",
            field=models.<PERSON><PERSON><PERSON><PERSON>(help_text="账号", max_length=255),
        ),
        migrations.AlterField(
            model_name="loginlog",
            name="department",
            field=models.Char<PERSON>ield(help_text="部门", max_length=255),
        ),
        migrations.AlterField(
            model_name="loginlog",
            name="ip",
            field=models.Char<PERSON>ield(help_text="登录IP", max_length=50),
        ),
        migrations.Alter<PERSON>ield(
            model_name="loginlog",
            name="login_time",
            field=models.DateTimeField(auto_now_add=True, help_text="登录时间"),
        ),
        migrations.AlterField(
            model_name="loginlog",
            name="logout_time",
            field=models.DateTimeField(blank=True, help_text="退出登录时间", null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name="loginlog",
            name="username",
            field=models.<PERSON><PERSON><PERSON><PERSON>(help_text="操作人", max_length=255),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="account",
            field=models.CharField(help_text="账号", max_length=255),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="belong",
            field=models.CharField(help_text="业务所属模块", max_length=255),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="button",
            field=models.CharField(help_text="功能按钮名称", max_length=255),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="ip",
            field=models.CharField(help_text="所在IP", max_length=50),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="kind",
            field=models.CharField(help_text="功能按钮类型", max_length=255),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="url",
            field=models.TextField(help_text="操作URL"),
        ),
        migrations.AlterField(
            model_name="operationlog",
            name="username",
            field=models.CharField(help_text="操作人", max_length=255),
        ),
    ]
