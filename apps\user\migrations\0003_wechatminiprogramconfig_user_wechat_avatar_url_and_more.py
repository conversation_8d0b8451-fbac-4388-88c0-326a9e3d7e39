# Generated by Django 4.1.13 on 2025-07-23 11:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0002_auto_20250702_1108'),
    ]

    operations = [
        migrations.CreateModel(
            name='WechatMiniProgramConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_name', models.CharField(help_text='微信小程序应用名称', max_length=100, verbose_name='应用名称')),
                ('app_id', models.CharField(help_text='微信小程序AppID', max_length=64, unique=True, verbose_name='AppID')),
                ('app_secret', models.CharField(help_text='微信小程序AppSecret', max_length=64, verbose_name='AppSecret')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('description', models.TextField(blank=True, null=True, verbose_name='描述信息')),
            ],
            options={
                'verbose_name': '微信小程序配置',
                'verbose_name_plural': '微信小程序配置',
                'db_table': 'wechat_miniprogram_config',
                'ordering': ['-created_time'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='wechat_avatar_url',
            field=models.URLField(blank=True, null=True, verbose_name='微信头像URL'),
        ),
        migrations.AddField(
            model_name='user',
            name='wechat_bind_time',
            field=models.DateTimeField(blank=True, null=True, verbose_name='微信绑定时间'),
        ),
        migrations.AddField(
            model_name='user',
            name='wechat_nickname',
            field=models.CharField(blank=True, max_length=64, null=True, verbose_name='微信昵称'),
        ),
        migrations.AddField(
            model_name='user',
            name='wechat_openid',
            field=models.CharField(blank=True, help_text='微信小程序用户唯一标识', max_length=64, null=True, unique=True, verbose_name='微信OpenID'),
        ),
        migrations.AddField(
            model_name='user',
            name='wechat_session_key',
            field=models.CharField(blank=True, help_text='微信会话密钥(加密存储)', max_length=128, null=True, verbose_name='微信SessionKey'),
        ),
        migrations.AddField(
            model_name='user',
            name='wechat_unionid',
            field=models.CharField(blank=True, help_text='微信开放平台用户唯一标识', max_length=64, null=True, verbose_name='微信UnionID'),
        ),
    ]
