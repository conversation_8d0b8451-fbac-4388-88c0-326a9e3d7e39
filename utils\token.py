#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : token.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc: OAuth2令牌管理工具类 - 授权服务器核心功能
"""

import json
import logging
import uuid
from typing import Any, Dict, Optional

# Django相关导入
from django.http import HttpRequest
# 第三方库导入
from oauth2_provider.views.base import RevokeTokenView, TokenView

# 设置日志
logger = logging.getLogger(__name__)


class OAuth2Error(Exception):
    """OAuth2相关异常"""
    pass


def _create_token_request(data: Dict[str, str]) -> HttpRequest:
    """
    创建OAuth2请求对象
    
    :param data: 请求数据字典
    :return: HttpRequest对象
    """
    request = HttpRequest()
    request.method = "POST"
    request.POST = data
    return request


def _handle_oauth2_response(response, operation: str = "token operation") -> Dict[str, Any]:
    """
    统一处理OAuth2响应
    
    :param response: OAuth2视图响应
    :param operation: 操作描述，用于日志
    :return: 解析后的响应数据
    :raises OAuth2Error: 当响应包含错误时
    """
    try:
        content = response.content.decode()
        if not content:
            return {"success": True, "message": f"{operation} completed"}
        
        data = json.loads(content)
        
        # 检查OAuth2错误响应
        if "error" in data:
            error_msg = f"{operation} failed: {data.get('error')} - {data.get('error_description', '')}"
            logger.error(error_msg)
            raise OAuth2Error(error_msg)
        
        logger.info(f"{operation} successful")
        return data
    
    except json.JSONDecodeError as e:
        error_msg = f"Failed to parse {operation} response: {e}"
        logger.error(error_msg)
        raise OAuth2Error(error_msg)
    except Exception as e:
        error_msg = f"Unexpected error in {operation}: {e}"
        logger.error(error_msg)
        raise OAuth2Error(error_msg)


def get_auth_token(
    username: str, 
    password: str, 
    client_id: str, 
    client_secret: str, 
    grant_type: str = "password",
    scope: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取OAuth2访问令牌 - 密码模式
    
    :param username: 用户名
    :param password: 密码
    :param client_id: 客户端ID
    :param client_secret: 客户端密钥
    :param grant_type: 授权类型，默认为password
    :param scope: 权限范围，可选（如：'read write'）
    :return: 包含access_token、refresh_token等的字典
    :raises OAuth2Error: 当认证失败时
    """
    data = {
        "grant_type": grant_type,
        "username": username,
        "password": password,
        "client_id": client_id,
        "client_secret": client_secret,
    }
    
    if scope:
        data["scope"] = scope
    
    request = _create_token_request(data)
    token_view = TokenView()
    response = token_view.post(request)
    
    return _handle_oauth2_response(response, "password grant token acquisition")


def get_client_token(
    client_id: str, 
    client_secret: str, 
    grant_type: str = "client_credentials",
    scope: Optional[str] = None
) -> Dict[str, Any]:
    """
    获取OAuth2访问令牌 - 客户端凭据模式
    
    :param client_id: 客户端ID
    :param client_secret: 客户端密钥
    :param grant_type: 授权类型，默认为client_credentials
    :param scope: 权限范围，可选
    :return: 包含access_token等的字典
    :raises OAuth2Error: 当认证失败时
    """
    data = {
        "grant_type": grant_type,
        "client_id": client_id,
        "client_secret": client_secret,
    }
    
    if scope:
        data["scope"] = scope
    
    request = _create_token_request(data)
    token_view = TokenView()
    response = token_view.post(request)
    
    return _handle_oauth2_response(response, "client credentials token acquisition")


def refresh_token(
    client_id: str, 
    client_secret: str, 
    refresh_token_value: str
) -> Dict[str, Any]:
    """
    刷新OAuth2访问令牌
    
    :param client_id: 客户端ID
    :param client_secret: 客户端密钥
    :param refresh_token_value: 刷新令牌值
    :return: 包含新access_token的字典
    :raises OAuth2Error: 当刷新失败时
    """
    data = {
        "grant_type": "refresh_token",
        "client_id": client_id,
        "client_secret": client_secret,
        "refresh_token": refresh_token_value,
    }
    
    request = _create_token_request(data)
    token_view = TokenView()
    response = token_view.post(request)
    
    return _handle_oauth2_response(response, "token refresh")


def revoke_token(
    client_id: str, 
    client_secret: str, 
    token: str, 
    token_type_hint: str = "access_token"
) -> Dict[str, Any]:
    """
    撤销OAuth2令牌
    
    :param client_id: 客户端ID
    :param client_secret: 客户端密钥
    :param token: 要撤销的令牌
    :param token_type_hint: 令牌类型提示（access_token 或 refresh_token）
    :return: 撤销结果字典
    :raises OAuth2Error: 当撤销失败时
    """
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "token": token,
        "token_type_hint": token_type_hint,
    }
    
    request = _create_token_request(data)
    revoke_view = RevokeTokenView()
    response = revoke_view.post(request)
    
    return _handle_oauth2_response(response, "token revocation")


def get_auth_token_with_scope(
    username: str, 
    password: str, 
    client_id: str, 
    client_secret: str, 
    scope: str = "read write"
) -> Dict[str, Any]:
    """
    获取带指定权限范围的访问令牌 - 增强版密码模式
    
    :param username: 用户名
    :param password: 密码
    :param client_id: 客户端ID
    :param client_secret: 客户端密钥
    :param scope: 权限范围，默认为"read write"
    :return: 包含access_token、refresh_token等的字典
    :raises OAuth2Error: 当认证失败时
    """
    return get_auth_token(username, password, client_id, client_secret, scope=scope)



def get_uuid4() -> str:
    """
    生成UUID4字符串

    :return: UUID4字符串，用于防重复提交等场景
    """
    return str(uuid.uuid4())



