#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : filters.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""

import django_filters
from django.apps import apps


class ApplicationFilter(django_filters.FilterSet):
    # 应用名称，模糊查询
    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")
    # 客户端id，模糊查询
    client_id = django_filters.CharFilter(
        field_name="client_id", lookup_expr="icontains"
    )
    # id为整数，精确查询
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")

    class Meta:
        model = apps.get_model("oauth2_provider", "Application")
        fields = ["name", "client_id", "id"]


class DepartmentFilter(django_filters.FilterSet):
    # id为整数，精确查询
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")
    # 部门名称，模糊查询
    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")

    class Meta:
        model = apps.get_model("auth", "Group")
        fields = ["id", "name"]


class PermissionFilter(django_filters.FilterSet):
    # 权限名称，模糊查询
    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")
    # 权限编码，模糊查询
    codename = django_filters.CharFilter(field_name="codename", lookup_expr="icontains")
    # id为整数，精确查询
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")
    # 应用id，精确查询
    application_id = django_filters.NumberFilter(
        field_name="application_id", lookup_expr="exact"
    )
    # 类型，精确查询
    permission_type = django_filters.CharFilter(
        field_name="permission_type", lookup_expr="exact"
    )

    class Meta:
        model = apps.get_model("auth", "Permission")
        fields = ["name", "codename", "id", "application_id", "permission_type"]


class RoleFilter(django_filters.FilterSet):
    # id为整数，精确查询
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")
    # 角色名称，模糊查询
    name = django_filters.CharFilter(field_name="name", lookup_expr="icontains")

    class Meta:
        model = apps.get_model("auth", "Group")
        fields = ["id", "name"]


class UserFilter(django_filters.FilterSet):
    # 用户名，模糊查询
    username = django_filters.CharFilter(field_name="username", lookup_expr="icontains")
    # 邮箱，模糊查询
    email = django_filters.CharFilter(field_name="email", lookup_expr="icontains")
    # id为整数，精确查询
    id = django_filters.NumberFilter(field_name="id", lookup_expr="exact")
    # 手机号码
    phone_number = django_filters.CharFilter(
        field_name="phone_number", lookup_expr="icontains"
    )
    # 身份找
    id_card_number = django_filters.CharFilter(
        field_name="id_card_number", lookup_expr="icontains"
    )
    # 通过部门名称查询用户，部门是外键，需要通过部门名称查询
    department_name = django_filters.CharFilter(method="filter_by_department_name")
    # 通过角色名称查询用户，角色是外键，需要通过角色名称查询
    role_name = django_filters.CharFilter(method="filter_by_role_name")

    class Meta:
        model = apps.get_model("user", "User")
        fields = ["username", "email", "id", "phone_number", "id_card_number"]

    def filter_by_department_name(self, queryset, name, value):
        # # 在部门的名称中进行模糊查询，部门的的开头d
        regex_pattern = r"^d.*" + value  # 以字母 "d" 开头的正则表达式模式
        Group = apps.get_model("auth", "Group")
        matching_departments = Group.objects.filter(name__iregex=regex_pattern)
        matching_users = queryset.filter(groups__in=matching_departments)
        return matching_users

    def filter_by_role_name(self, queryset, name, value):
        # 在角色的名称中进行模糊查询，角色的的开头r
        regex_pattern = r"^r.*" + value  # 以字母 "r" 开头的正则表达式模式
        Group = apps.get_model("auth", "Group")
        matching_roles = Group.objects.filter(name__iregex=regex_pattern)
        matching_users = queryset.filter(groups__in=matching_roles)
        return matching_users
