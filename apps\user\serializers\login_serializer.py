#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : login_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""

from rest_framework import serializers


class UserLoginSerializer(serializers.Serializer):
    """登录参数序列化器"""

    username = serializers.CharField(required=True, max_length=128, label="username")
    password = serializers.CharField(required=True, max_length=256, label="password")
    captcha = serializers.CharField(required=True, max_length=4, label="captcha")
    client_id = serializers.Char<PERSON>ield(required=False, label="client_id")
    client_secret = serializers.Char<PERSON>ield(required=False, label="client_secret")


class RefreshSerializer(serializers.Serializer):
    """刷新令牌参数序列化器"""

    refresh_token = serializers.CharField(required=True, label="refresh_token")
    client_id = serializers.Char<PERSON><PERSON>(required=True, label="client_id")
    client_secret = serializers.Char<PERSON><PERSON>(required=True, label="client_secret")


class UserLogoutSerializer(serializers.Serializer):
    """退出登录参数序列化器"""

    # token: REQUIRED, this is the Access Token you want to revoke
    token = serializers.CharField(required=True, label="access_token")
    # token_type_hint: OPTIONAL, designating either ‘access_token’ or ‘refresh_token’.
    token_type = serializers.ChoiceField(
        choices=["access_token", "refresh_token"], required=False, label="token_type"
    )


class CapchaSerializer(serializers.Serializer):
    """验证码参数序列化器"""

    captcha_type = serializers.ChoiceField(
        choices=["login", "register", "forget", "reset"],
        required=True,
        label="captcha_type",
    )
