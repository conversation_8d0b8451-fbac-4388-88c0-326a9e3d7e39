#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : application_serializer.py
<AUTHOR> JT_DA
@Date     : 2025/06/30
@File_Desc:
"""

from django.apps import apps
from django.db import transaction
from oauth2_provider.generators import generate_client_id, generate_client_secret
from oauth2_provider.models import get_application_model

from apps.user.serializers.base import DynamicFieldsModelSerializer


class ApplicationSerializer(DynamicFieldsModelSerializer):
    """应用的序列化器"""

    class Meta:
        model = apps.get_model("oauth2_provider", "Application")
        fields = (
            "id",
            "name",
            "client_id",
            "client_type",
            "authorization_grant_type",
            "redirect_uris",
        )

    # 数据库新增操作解耦
    def create(self, validated_data):
        name = validated_data.get("name")
        redirect_uris = validated_data.get("redirect_uris", "")
        client_type = validated_data.get("client_type")
        authorization_grant_type = validated_data.get("authorization_grant_type")
        client_id = generate_client_id()
        client_secret = generate_client_secret()

        application_dict = {
            "name": name,
            "redirect_uris": redirect_uris,
            "client_type": client_type,
            "authorization_grant_type": authorization_grant_type,
            "client_id": client_id,
            "client_secret": client_secret,
        }

        print(application_dict)

        with transaction.atomic():
            app = get_application_model().objects.create(**application_dict)

        return app
