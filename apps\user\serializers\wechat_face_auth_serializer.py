#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File     : wechat_face_auth_serializer.py
<AUTHOR> AI Assistant
@Date     : 2025/07/24
@File_Desc: 微信用户人脸核身相关序列化器
"""

from rest_framework import serializers
from django.utils import timezone
from apps.user.models import User


class WechatFaceAuthUpdateSerializer(serializers.Serializer):
    """
    微信用户人脸核身信息更新序列化器

    用于验证和处理微信用户人脸核身后的信息更新请求。
    所有字段均为可选，支持部分更新用户的人脸核身相关信息。
    """

    real_name = serializers.CharField(
        max_length=100, required=False, allow_blank=True, help_text="用户的真实姓名，通过人脸核身验证获得"
    )

    id_card_number = serializers.CharField(
        max_length=18, required=False, allow_blank=True, help_text="用户的身份证号码"
    )

    biz_token = serializers.Char<PERSON><PERSON>(
        max_length=255, required=False, allow_blank=True, help_text="人脸核身流程的唯一业务标识，用于追踪核身流程"
    )

    detect_auth_time = serializers.DateTimeField(required=False, allow_null=True, help_text="人脸核身完成的日期时间")

    detect_auth_result = serializers.BooleanField(
        required=False, allow_null=True, help_text="人脸核身结果：True表示核身成功，False表示核身失败"
    )

    def validate_id_card_number(self, value):
        """
        验证身份证号码格式

        Args:
            value: 身份证号码字符串

        Returns:
            str: 验证通过的身份证号码

        Raises:
            ValidationError: 当身份证号码格式不正确时
        """
        if not value:  # 允许空值
            return value

        # 基本长度检查
        if len(value) not in [15, 18]:
            raise serializers.ValidationError("身份证号码长度必须为15位或18位")

        # 18位身份证号码格式检查
        if len(value) == 18:
            # 前17位必须是数字
            if not value[:17].isdigit():
                raise serializers.ValidationError("身份证号码前17位必须为数字")
            # 最后一位可以是数字或X
            if not (value[17].isdigit() or value[17].upper() == "X"):
                raise serializers.ValidationError("身份证号码最后一位必须为数字或X")
        else:
            # 15位身份证号码必须全为数字
            if not value.isdigit():
                raise serializers.ValidationError("15位身份证号码必须全为数字")

        return value

    def validate_real_name(self, value):
        """
        验证真实姓名格式

        Args:
            value: 真实姓名字符串

        Returns:
            str: 验证通过的真实姓名

        Raises:
            ValidationError: 当姓名格式不正确时
        """
        if not value:  # 允许空值
            return value

        # 去除首尾空格
        value = value.strip()

        # 长度检查
        if len(value) < 2 or len(value) > 50:
            raise serializers.ValidationError("真实姓名长度必须在2-50个字符之间")

        return value

    def update_user_face_auth_info(self, user, validated_data):
        """
        更新用户的人脸核身相关信息

        Args:
            user: 要更新的用户实例
            validated_data: 验证后的数据字典

        Returns:
            User: 更新后的用户实例
        """
        # 只更新提供的字段
        for field_name, value in validated_data.items():
            if hasattr(user, field_name):
                setattr(user, field_name, value)

        # 如果更新了核身结果且为成功，自动设置核身时间（如果未提供）
        if (
            validated_data.get("detect_auth_result") is True
            and "detect_auth_time" not in validated_data
            and not user.detect_auth_time
        ):
            user.detect_auth_time = timezone.now()

        user.save()
        return user


